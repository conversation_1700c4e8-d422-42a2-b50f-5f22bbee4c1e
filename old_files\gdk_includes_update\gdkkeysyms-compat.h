/* GDK - The GIMP Drawing Kit
 * Copyright (C) 1995-1997 <PERSON>, <PERSON> and <PERSON>
 * Copyright (C) 2005, 2006, 2007, 2009 GNOME Foundation
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the
 * Free Software Foundation, Inc., 59 Temple Place - Suite 330,
 * Boston, MA 02111-1307, USA.
 */

/*
 * Compatibility version of gdkkeysyms.h.
 *
 * In GTK3, keysyms changed to have a KEY_ prefix.  This is a compatibility header
 * your application can include to gain access to the old names as well.  Consider
 * porting to the new names instead.
 */

#ifndef __GDK_KEYSYMS_COMPAT_H__
#define __GDK_KEYSYMS_COMPAT_H__

#define GDK_VoidSymbol 0xffffff
#define GDK_BackSpace 0xff08
#define GDK_Tab 0xff09
#define GDK_Linefeed 0xff0a
#define GDK_Clear 0xff0b
#define GDK_Return 0xff0d
#define GDK_Pause 0xff13
#define GDK_Scroll_Lock 0xff14
#define GDK_Sys_Req 0xff15
#define GDK_Escape 0xff1b
#define GDK_Delete 0xffff
#define GDK_Multi_key 0xff20
#define GDK_Codeinput 0xff37
#define GDK_SingleCandidate 0xff3c
#define GDK_MultipleCandidate 0xff3d
#define GDK_PreviousCandidate 0xff3e
#define GDK_Kanji 0xff21
#define GDK_Muhenkan 0xff22
#define GDK_Henkan_Mode 0xff23
#define GDK_Henkan 0xff23
#define GDK_Romaji 0xff24
#define GDK_Hiragana 0xff25
#define GDK_Katakana 0xff26
#define GDK_Hiragana_Katakana 0xff27
#define GDK_Zenkaku 0xff28
#define GDK_Hankaku 0xff29
#define GDK_Zenkaku_Hankaku 0xff2a
#define GDK_Touroku 0xff2b
#define GDK_Massyo 0xff2c
#define GDK_Kana_Lock 0xff2d
#define GDK_Kana_Shift 0xff2e
#define GDK_Eisu_Shift 0xff2f
#define GDK_Eisu_toggle 0xff30
#define GDK_Kanji_Bangou 0xff37
#define GDK_Zen_Koho 0xff3d
#define GDK_Mae_Koho 0xff3e
#define GDK_Home 0xff50
#define GDK_Left 0xff51
#define GDK_Up 0xff52
#define GDK_Right 0xff53
#define GDK_Down 0xff54
#define GDK_Prior 0xff55
#define GDK_Page_Up 0xff55
#define GDK_Next 0xff56
#define GDK_Page_Down 0xff56
#define GDK_End 0xff57
#define GDK_Begin 0xff58
#define GDK_Select 0xff60
#define GDK_Print 0xff61
#define GDK_Execute 0xff62
#define GDK_Insert 0xff63
#define GDK_Undo 0xff65
#define GDK_Redo 0xff66
#define GDK_Menu 0xff67
#define GDK_Find 0xff68
#define GDK_Cancel 0xff69
#define GDK_Help 0xff6a
#define GDK_Break 0xff6b
#define GDK_Mode_switch 0xff7e
#define GDK_script_switch 0xff7e
#define GDK_Num_Lock 0xff7f
#define GDK_KP_Space 0xff80
#define GDK_KP_Tab 0xff89
#define GDK_KP_Enter 0xff8d
#define GDK_KP_F1 0xff91
#define GDK_KP_F2 0xff92
#define GDK_KP_F3 0xff93
#define GDK_KP_F4 0xff94
#define GDK_KP_Home 0xff95
#define GDK_KP_Left 0xff96
#define GDK_KP_Up 0xff97
#define GDK_KP_Right 0xff98
#define GDK_KP_Down 0xff99
#define GDK_KP_Prior 0xff9a
#define GDK_KP_Page_Up 0xff9a
#define GDK_KP_Next 0xff9b
#define GDK_KP_Page_Down 0xff9b
#define GDK_KP_End 0xff9c
#define GDK_KP_Begin 0xff9d
#define GDK_KP_Insert 0xff9e
#define GDK_KP_Delete 0xff9f
#define GDK_KP_Equal 0xffbd
#define GDK_KP_Multiply 0xffaa
#define GDK_KP_Add 0xffab
#define GDK_KP_Separator 0xffac
#define GDK_KP_Subtract 0xffad
#define GDK_KP_Decimal 0xffae
#define GDK_KP_Divide 0xffaf
#define GDK_KP_0 0xffb0
#define GDK_KP_1 0xffb1
#define GDK_KP_2 0xffb2
#define GDK_KP_3 0xffb3
#define GDK_KP_4 0xffb4
#define GDK_KP_5 0xffb5
#define GDK_KP_6 0xffb6
#define GDK_KP_7 0xffb7
#define GDK_KP_8 0xffb8
#define GDK_KP_9 0xffb9
#define GDK_F1 0xffbe
#define GDK_F2 0xffbf
#define GDK_F3 0xffc0
#define GDK_F4 0xffc1
#define GDK_F5 0xffc2
#define GDK_F6 0xffc3
#define GDK_F7 0xffc4
#define GDK_F8 0xffc5
#define GDK_F9 0xffc6
#define GDK_F10 0xffc7
#define GDK_F11 0xffc8
#define GDK_L1 0xffc8
#define GDK_F12 0xffc9
#define GDK_L2 0xffc9
#define GDK_F13 0xffca
#define GDK_L3 0xffca
#define GDK_F14 0xffcb
#define GDK_L4 0xffcb
#define GDK_F15 0xffcc
#define GDK_L5 0xffcc
#define GDK_F16 0xffcd
#define GDK_L6 0xffcd
#define GDK_F17 0xffce
#define GDK_L7 0xffce
#define GDK_F18 0xffcf
#define GDK_L8 0xffcf
#define GDK_F19 0xffd0
#define GDK_L9 0xffd0
#define GDK_F20 0xffd1
#define GDK_L10 0xffd1
#define GDK_F21 0xffd2
#define GDK_R1 0xffd2
#define GDK_F22 0xffd3
#define GDK_R2 0xffd3
#define GDK_F23 0xffd4
#define GDK_R3 0xffd4
#define GDK_F24 0xffd5
#define GDK_R4 0xffd5
#define GDK_F25 0xffd6
#define GDK_R5 0xffd6
#define GDK_F26 0xffd7
#define GDK_R6 0xffd7
#define GDK_F27 0xffd8
#define GDK_R7 0xffd8
#define GDK_F28 0xffd9
#define GDK_R8 0xffd9
#define GDK_F29 0xffda
#define GDK_R9 0xffda
#define GDK_F30 0xffdb
#define GDK_R10 0xffdb
#define GDK_F31 0xffdc
#define GDK_R11 0xffdc
#define GDK_F32 0xffdd
#define GDK_R12 0xffdd
#define GDK_F33 0xffde
#define GDK_R13 0xffde
#define GDK_F34 0xffdf
#define GDK_R14 0xffdf
#define GDK_F35 0xffe0
#define GDK_R15 0xffe0
#define GDK_Shift_L 0xffe1
#define GDK_Shift_R 0xffe2
#define GDK_Control_L 0xffe3
#define GDK_Control_R 0xffe4
#define GDK_Caps_Lock 0xffe5
#define GDK_Shift_Lock 0xffe6
#define GDK_Meta_L 0xffe7
#define GDK_Meta_R 0xffe8
#define GDK_Alt_L 0xffe9
#define GDK_Alt_R 0xffea
#define GDK_Super_L 0xffeb
#define GDK_Super_R 0xffec
#define GDK_Hyper_L 0xffed
#define GDK_Hyper_R 0xffee
#define GDK_ISO_Lock 0xfe01
#define GDK_ISO_Level2_Latch 0xfe02
#define GDK_ISO_Level3_Shift 0xfe03
#define GDK_ISO_Level3_Latch 0xfe04
#define GDK_ISO_Level3_Lock 0xfe05
#define GDK_ISO_Level5_Shift 0xfe11
#define GDK_ISO_Level5_Latch 0xfe12
#define GDK_ISO_Level5_Lock 0xfe13
#define GDK_ISO_Group_Shift 0xff7e
#define GDK_ISO_Group_Latch 0xfe06
#define GDK_ISO_Group_Lock 0xfe07
#define GDK_ISO_Next_Group 0xfe08
#define GDK_ISO_Next_Group_Lock 0xfe09
#define GDK_ISO_Prev_Group 0xfe0a
#define GDK_ISO_Prev_Group_Lock 0xfe0b
#define GDK_ISO_First_Group 0xfe0c
#define GDK_ISO_First_Group_Lock 0xfe0d
#define GDK_ISO_Last_Group 0xfe0e
#define GDK_ISO_Last_Group_Lock 0xfe0f
#define GDK_ISO_Left_Tab 0xfe20
#define GDK_ISO_Move_Line_Up 0xfe21
#define GDK_ISO_Move_Line_Down 0xfe22
#define GDK_ISO_Partial_Line_Up 0xfe23
#define GDK_ISO_Partial_Line_Down 0xfe24
#define GDK_ISO_Partial_Space_Left 0xfe25
#define GDK_ISO_Partial_Space_Right 0xfe26
#define GDK_ISO_Set_Margin_Left 0xfe27
#define GDK_ISO_Set_Margin_Right 0xfe28
#define GDK_ISO_Release_Margin_Left 0xfe29
#define GDK_ISO_Release_Margin_Right 0xfe2a
#define GDK_ISO_Release_Both_Margins 0xfe2b
#define GDK_ISO_Fast_Cursor_Left 0xfe2c
#define GDK_ISO_Fast_Cursor_Right 0xfe2d
#define GDK_ISO_Fast_Cursor_Up 0xfe2e
#define GDK_ISO_Fast_Cursor_Down 0xfe2f
#define GDK_ISO_Continuous_Underline 0xfe30
#define GDK_ISO_Discontinuous_Underline 0xfe31
#define GDK_ISO_Emphasize 0xfe32
#define GDK_ISO_Center_Object 0xfe33
#define GDK_ISO_Enter 0xfe34
#define GDK_dead_grave 0xfe50
#define GDK_dead_acute 0xfe51
#define GDK_dead_circumflex 0xfe52
#define GDK_dead_tilde 0xfe53
#define GDK_dead_perispomeni 0xfe53
#define GDK_dead_macron 0xfe54
#define GDK_dead_breve 0xfe55
#define GDK_dead_abovedot 0xfe56
#define GDK_dead_diaeresis 0xfe57
#define GDK_dead_abovering 0xfe58
#define GDK_dead_doubleacute 0xfe59
#define GDK_dead_caron 0xfe5a
#define GDK_dead_cedilla 0xfe5b
#define GDK_dead_ogonek 0xfe5c
#define GDK_dead_iota 0xfe5d
#define GDK_dead_voiced_sound 0xfe5e
#define GDK_dead_semivoiced_sound 0xfe5f
#define GDK_dead_belowdot 0xfe60
#define GDK_dead_hook 0xfe61
#define GDK_dead_horn 0xfe62
#define GDK_dead_stroke 0xfe63
#define GDK_dead_abovecomma 0xfe64
#define GDK_dead_psili 0xfe64
#define GDK_dead_abovereversedcomma 0xfe65
#define GDK_dead_dasia 0xfe65
#define GDK_dead_doublegrave 0xfe66
#define GDK_dead_belowring 0xfe67
#define GDK_dead_belowmacron 0xfe68
#define GDK_dead_belowcircumflex 0xfe69
#define GDK_dead_belowtilde 0xfe6a
#define GDK_dead_belowbreve 0xfe6b
#define GDK_dead_belowdiaeresis 0xfe6c
#define GDK_dead_invertedbreve 0xfe6d
#define GDK_dead_belowcomma 0xfe6e
#define GDK_dead_currency 0xfe6f
#define GDK_dead_a 0xfe80
#define GDK_dead_A 0xfe81
#define GDK_dead_e 0xfe82
#define GDK_dead_E 0xfe83
#define GDK_dead_i 0xfe84
#define GDK_dead_I 0xfe85
#define GDK_dead_o 0xfe86
#define GDK_dead_O 0xfe87
#define GDK_dead_u 0xfe88
#define GDK_dead_U 0xfe89
#define GDK_dead_small_schwa 0xfe8a
#define GDK_dead_capital_schwa 0xfe8b
#define GDK_First_Virtual_Screen 0xfed0
#define GDK_Prev_Virtual_Screen 0xfed1
#define GDK_Next_Virtual_Screen 0xfed2
#define GDK_Last_Virtual_Screen 0xfed4
#define GDK_Terminate_Server 0xfed5
#define GDK_AccessX_Enable 0xfe70
#define GDK_AccessX_Feedback_Enable 0xfe71
#define GDK_RepeatKeys_Enable 0xfe72
#define GDK_SlowKeys_Enable 0xfe73
#define GDK_BounceKeys_Enable 0xfe74
#define GDK_StickyKeys_Enable 0xfe75
#define GDK_MouseKeys_Enable 0xfe76
#define GDK_MouseKeys_Accel_Enable 0xfe77
#define GDK_Overlay1_Enable 0xfe78
#define GDK_Overlay2_Enable 0xfe79
#define GDK_AudibleBell_Enable 0xfe7a
#define GDK_Pointer_Left 0xfee0
#define GDK_Pointer_Right 0xfee1
#define GDK_Pointer_Up 0xfee2
#define GDK_Pointer_Down 0xfee3
#define GDK_Pointer_UpLeft 0xfee4
#define GDK_Pointer_UpRight 0xfee5
#define GDK_Pointer_DownLeft 0xfee6
#define GDK_Pointer_DownRight 0xfee7
#define GDK_Pointer_Button_Dflt 0xfee8
#define GDK_Pointer_Button1 0xfee9
#define GDK_Pointer_Button2 0xfeea
#define GDK_Pointer_Button3 0xfeeb
#define GDK_Pointer_Button4 0xfeec
#define GDK_Pointer_Button5 0xfeed
#define GDK_Pointer_DblClick_Dflt 0xfeee
#define GDK_Pointer_DblClick1 0xfeef
#define GDK_Pointer_DblClick2 0xfef0
#define GDK_Pointer_DblClick3 0xfef1
#define GDK_Pointer_DblClick4 0xfef2
#define GDK_Pointer_DblClick5 0xfef3
#define GDK_Pointer_Drag_Dflt 0xfef4
#define GDK_Pointer_Drag1 0xfef5
#define GDK_Pointer_Drag2 0xfef6
#define GDK_Pointer_Drag3 0xfef7
#define GDK_Pointer_Drag4 0xfef8
#define GDK_Pointer_Drag5 0xfefd
#define GDK_Pointer_EnableKeys 0xfef9
#define GDK_Pointer_Accelerate 0xfefa
#define GDK_Pointer_DfltBtnNext 0xfefb
#define GDK_Pointer_DfltBtnPrev 0xfefc
#define GDK_3270_Duplicate 0xfd01
#define GDK_3270_FieldMark 0xfd02
#define GDK_3270_Right2 0xfd03
#define GDK_3270_Left2 0xfd04
#define GDK_3270_BackTab 0xfd05
#define GDK_3270_EraseEOF 0xfd06
#define GDK_3270_EraseInput 0xfd07
#define GDK_3270_Reset 0xfd08
#define GDK_3270_Quit 0xfd09
#define GDK_3270_PA1 0xfd0a
#define GDK_3270_PA2 0xfd0b
#define GDK_3270_PA3 0xfd0c
#define GDK_3270_Test 0xfd0d
#define GDK_3270_Attn 0xfd0e
#define GDK_3270_CursorBlink 0xfd0f
#define GDK_3270_AltCursor 0xfd10
#define GDK_3270_KeyClick 0xfd11
#define GDK_3270_Jump 0xfd12
#define GDK_3270_Ident 0xfd13
#define GDK_3270_Rule 0xfd14
#define GDK_3270_Copy 0xfd15
#define GDK_3270_Play 0xfd16
#define GDK_3270_Setup 0xfd17
#define GDK_3270_Record 0xfd18
#define GDK_3270_ChangeScreen 0xfd19
#define GDK_3270_DeleteWord 0xfd1a
#define GDK_3270_ExSelect 0xfd1b
#define GDK_3270_CursorSelect 0xfd1c
#define GDK_3270_PrintScreen 0xfd1d
#define GDK_3270_Enter 0xfd1e
#define GDK_space 0x020
#define GDK_exclam 0x021
#define GDK_quotedbl 0x022
#define GDK_numbersign 0x023
#define GDK_dollar 0x024
#define GDK_percent 0x025
#define GDK_ampersand 0x026
#define GDK_apostrophe 0x027
#define GDK_quoteright 0x027
#define GDK_parenleft 0x028
#define GDK_parenright 0x029
#define GDK_asterisk 0x02a
#define GDK_plus 0x02b
#define GDK_comma 0x02c
#define GDK_minus 0x02d
#define GDK_period 0x02e
#define GDK_slash 0x02f
#define GDK_0 0x030
#define GDK_1 0x031
#define GDK_2 0x032
#define GDK_3 0x033
#define GDK_4 0x034
#define GDK_5 0x035
#define GDK_6 0x036
#define GDK_7 0x037
#define GDK_8 0x038
#define GDK_9 0x039
#define GDK_colon 0x03a
#define GDK_semicolon 0x03b
#define GDK_less 0x03c
#define GDK_equal 0x03d
#define GDK_greater 0x03e
#define GDK_question 0x03f
#define GDK_at 0x040
#define GDK_A 0x041
#define GDK_B 0x042
#define GDK_C 0x043
#define GDK_D 0x044
#define GDK_E 0x045
#define GDK_F 0x046
#define GDK_G 0x047
#define GDK_H 0x048
#define GDK_I 0x049
#define GDK_J 0x04a
#define GDK_K 0x04b
#define GDK_L 0x04c
#define GDK_M 0x04d
#define GDK_N 0x04e
#define GDK_O 0x04f
#define GDK_P 0x050
#define GDK_Q 0x051
#define GDK_R 0x052
#define GDK_S 0x053
#define GDK_T 0x054
#define GDK_U 0x055
#define GDK_V 0x056
#define GDK_W 0x057
#define GDK_X 0x058
#define GDK_Y 0x059
#define GDK_Z 0x05a
#define GDK_bracketleft 0x05b
#define GDK_backslash 0x05c
#define GDK_bracketright 0x05d
#define GDK_asciicircum 0x05e
#define GDK_underscore 0x05f
#define GDK_grave 0x060
#define GDK_quoteleft 0x060
#define GDK_a 0x061
#define GDK_b 0x062
#define GDK_c 0x063
#define GDK_d 0x064
#define GDK_e 0x065
#define GDK_f 0x066
#define GDK_g 0x067
#define GDK_h 0x068
#define GDK_i 0x069
#define GDK_j 0x06a
#define GDK_k 0x06b
#define GDK_l 0x06c
#define GDK_m 0x06d
#define GDK_n 0x06e
#define GDK_o 0x06f
#define GDK_p 0x070
#define GDK_q 0x071
#define GDK_r 0x072
#define GDK_s 0x073
#define GDK_t 0x074
#define GDK_u 0x075
#define GDK_v 0x076
#define GDK_w 0x077
#define GDK_x 0x078
#define GDK_y 0x079
#define GDK_z 0x07a
#define GDK_braceleft 0x07b
#define GDK_bar 0x07c
#define GDK_braceright 0x07d
#define GDK_asciitilde 0x07e
#define GDK_nobreakspace 0x0a0
#define GDK_exclamdown 0x0a1
#define GDK_cent 0x0a2
#define GDK_sterling 0x0a3
#define GDK_currency 0x0a4
#define GDK_yen 0x0a5
#define GDK_brokenbar 0x0a6
#define GDK_section 0x0a7
#define GDK_diaeresis 0x0a8
#define GDK_copyright 0x0a9
#define GDK_ordfeminine 0x0aa
#define GDK_guillemotleft 0x0ab
#define GDK_notsign 0x0ac
#define GDK_hyphen 0x0ad
#define GDK_registered 0x0ae
#define GDK_macron 0x0af
#define GDK_degree 0x0b0
#define GDK_plusminus 0x0b1
#define GDK_twosuperior 0x0b2
#define GDK_threesuperior 0x0b3
#define GDK_acute 0x0b4
#define GDK_mu 0x0b5
#define GDK_paragraph 0x0b6
#define GDK_periodcentered 0x0b7
#define GDK_cedilla 0x0b8
#define GDK_onesuperior 0x0b9
#define GDK_masculine 0x0ba
#define GDK_guillemotright 0x0bb
#define GDK_onequarter 0x0bc
#define GDK_onehalf 0x0bd
#define GDK_threequarters 0x0be
#define GDK_questiondown 0x0bf
#define GDK_Agrave 0x0c0
#define GDK_Aacute 0x0c1
#define GDK_Acircumflex 0x0c2
#define GDK_Atilde 0x0c3
#define GDK_Adiaeresis 0x0c4
#define GDK_Aring 0x0c5
#define GDK_AE 0x0c6
#define GDK_Ccedilla 0x0c7
#define GDK_Egrave 0x0c8
#define GDK_Eacute 0x0c9
#define GDK_Ecircumflex 0x0ca
#define GDK_Ediaeresis 0x0cb
#define GDK_Igrave 0x0cc
#define GDK_Iacute 0x0cd
#define GDK_Icircumflex 0x0ce
#define GDK_Idiaeresis 0x0cf
#define GDK_ETH 0x0d0
#define GDK_Eth 0x0d0
#define GDK_Ntilde 0x0d1
#define GDK_Ograve 0x0d2
#define GDK_Oacute 0x0d3
#define GDK_Ocircumflex 0x0d4
#define GDK_Otilde 0x0d5
#define GDK_Odiaeresis 0x0d6
#define GDK_multiply 0x0d7
#define GDK_Oslash 0x0d8
#define GDK_Ooblique 0x0d8
#define GDK_Ugrave 0x0d9
#define GDK_Uacute 0x0da
#define GDK_Ucircumflex 0x0db
#define GDK_Udiaeresis 0x0dc
#define GDK_Yacute 0x0dd
#define GDK_THORN 0x0de
#define GDK_Thorn 0x0de
#define GDK_ssharp 0x0df
#define GDK_agrave 0x0e0
#define GDK_aacute 0x0e1
#define GDK_acircumflex 0x0e2
#define GDK_atilde 0x0e3
#define GDK_adiaeresis 0x0e4
#define GDK_aring 0x0e5
#define GDK_ae 0x0e6
#define GDK_ccedilla 0x0e7
#define GDK_egrave 0x0e8
#define GDK_eacute 0x0e9
#define GDK_ecircumflex 0x0ea
#define GDK_ediaeresis 0x0eb
#define GDK_igrave 0x0ec
#define GDK_iacute 0x0ed
#define GDK_icircumflex 0x0ee
#define GDK_idiaeresis 0x0ef
#define GDK_eth 0x0f0
#define GDK_ntilde 0x0f1
#define GDK_ograve 0x0f2
#define GDK_oacute 0x0f3
#define GDK_ocircumflex 0x0f4
#define GDK_otilde 0x0f5
#define GDK_odiaeresis 0x0f6
#define GDK_division 0x0f7
#define GDK_oslash 0x0f8
#define GDK_ooblique 0x0f8
#define GDK_ugrave 0x0f9
#define GDK_uacute 0x0fa
#define GDK_ucircumflex 0x0fb
#define GDK_udiaeresis 0x0fc
#define GDK_yacute 0x0fd
#define GDK_thorn 0x0fe
#define GDK_ydiaeresis 0x0ff
#define GDK_Aogonek 0x1a1
#define GDK_breve 0x1a2
#define GDK_Lstroke 0x1a3
#define GDK_Lcaron 0x1a5
#define GDK_Sacute 0x1a6
#define GDK_Scaron 0x1a9
#define GDK_Scedilla 0x1aa
#define GDK_Tcaron 0x1ab
#define GDK_Zacute 0x1ac
#define GDK_Zcaron 0x1ae
#define GDK_Zabovedot 0x1af
#define GDK_aogonek 0x1b1
#define GDK_ogonek 0x1b2
#define GDK_lstroke 0x1b3
#define GDK_lcaron 0x1b5
#define GDK_sacute 0x1b6
#define GDK_caron 0x1b7
#define GDK_scaron 0x1b9
#define GDK_scedilla 0x1ba
#define GDK_tcaron 0x1bb
#define GDK_zacute 0x1bc
#define GDK_doubleacute 0x1bd
#define GDK_zcaron 0x1be
#define GDK_zabovedot 0x1bf
#define GDK_Racute 0x1c0
#define GDK_Abreve 0x1c3
#define GDK_Lacute 0x1c5
#define GDK_Cacute 0x1c6
#define GDK_Ccaron 0x1c8
#define GDK_Eogonek 0x1ca
#define GDK_Ecaron 0x1cc
#define GDK_Dcaron 0x1cf
#define GDK_Dstroke 0x1d0
#define GDK_Nacute 0x1d1
#define GDK_Ncaron 0x1d2
#define GDK_Odoubleacute 0x1d5
#define GDK_Rcaron 0x1d8
#define GDK_Uring 0x1d9
#define GDK_Udoubleacute 0x1db
#define GDK_Tcedilla 0x1de
#define GDK_racute 0x1e0
#define GDK_abreve 0x1e3
#define GDK_lacute 0x1e5
#define GDK_cacute 0x1e6
#define GDK_ccaron 0x1e8
#define GDK_eogonek 0x1ea
#define GDK_ecaron 0x1ec
#define GDK_dcaron 0x1ef
#define GDK_dstroke 0x1f0
#define GDK_nacute 0x1f1
#define GDK_ncaron 0x1f2
#define GDK_odoubleacute 0x1f5
#define GDK_udoubleacute 0x1fb
#define GDK_rcaron 0x1f8
#define GDK_uring 0x1f9
#define GDK_tcedilla 0x1fe
#define GDK_abovedot 0x1ff
#define GDK_Hstroke 0x2a1
#define GDK_Hcircumflex 0x2a6
#define GDK_Iabovedot 0x2a9
#define GDK_Gbreve 0x2ab
#define GDK_Jcircumflex 0x2ac
#define GDK_hstroke 0x2b1
#define GDK_hcircumflex 0x2b6
#define GDK_idotless 0x2b9
#define GDK_gbreve 0x2bb
#define GDK_jcircumflex 0x2bc
#define GDK_Cabovedot 0x2c5
#define GDK_Ccircumflex 0x2c6
#define GDK_Gabovedot 0x2d5
#define GDK_Gcircumflex 0x2d8
#define GDK_Ubreve 0x2dd
#define GDK_Scircumflex 0x2de
#define GDK_cabovedot 0x2e5
#define GDK_ccircumflex 0x2e6
#define GDK_gabovedot 0x2f5
#define GDK_gcircumflex 0x2f8
#define GDK_ubreve 0x2fd
#define GDK_scircumflex 0x2fe
#define GDK_kra 0x3a2
#define GDK_kappa 0x3a2
#define GDK_Rcedilla 0x3a3
#define GDK_Itilde 0x3a5
#define GDK_Lcedilla 0x3a6
#define GDK_Emacron 0x3aa
#define GDK_Gcedilla 0x3ab
#define GDK_Tslash 0x3ac
#define GDK_rcedilla 0x3b3
#define GDK_itilde 0x3b5
#define GDK_lcedilla 0x3b6
#define GDK_emacron 0x3ba
#define GDK_gcedilla 0x3bb
#define GDK_tslash 0x3bc
#define GDK_ENG 0x3bd
#define GDK_eng 0x3bf
#define GDK_Amacron 0x3c0
#define GDK_Iogonek 0x3c7
#define GDK_Eabovedot 0x3cc
#define GDK_Imacron 0x3cf
#define GDK_Ncedilla 0x3d1
#define GDK_Omacron 0x3d2
#define GDK_Kcedilla 0x3d3
#define GDK_Uogonek 0x3d9
#define GDK_Utilde 0x3dd
#define GDK_Umacron 0x3de
#define GDK_amacron 0x3e0
#define GDK_iogonek 0x3e7
#define GDK_eabovedot 0x3ec
#define GDK_imacron 0x3ef
#define GDK_ncedilla 0x3f1
#define GDK_omacron 0x3f2
#define GDK_kcedilla 0x3f3
#define GDK_uogonek 0x3f9
#define GDK_utilde 0x3fd
#define GDK_umacron 0x3fe
#define GDK_Babovedot 0x1001e02
#define GDK_babovedot 0x1001e03
#define GDK_Dabovedot 0x1001e0a
#define GDK_Wgrave 0x1001e80
#define GDK_Wacute 0x1001e82
#define GDK_dabovedot 0x1001e0b
#define GDK_Ygrave 0x1001ef2
#define GDK_Fabovedot 0x1001e1e
#define GDK_fabovedot 0x1001e1f
#define GDK_Mabovedot 0x1001e40
#define GDK_mabovedot 0x1001e41
#define GDK_Pabovedot 0x1001e56
#define GDK_wgrave 0x1001e81
#define GDK_pabovedot 0x1001e57
#define GDK_wacute 0x1001e83
#define GDK_Sabovedot 0x1001e60
#define GDK_ygrave 0x1001ef3
#define GDK_Wdiaeresis 0x1001e84
#define GDK_wdiaeresis 0x1001e85
#define GDK_sabovedot 0x1001e61
#define GDK_Wcircumflex 0x1000174
#define GDK_Tabovedot 0x1001e6a
#define GDK_Ycircumflex 0x1000176
#define GDK_wcircumflex 0x1000175
#define GDK_tabovedot 0x1001e6b
#define GDK_ycircumflex 0x1000177
#define GDK_OE 0x13bc
#define GDK_oe 0x13bd
#define GDK_Ydiaeresis 0x13be
#define GDK_overline 0x47e
#define GDK_kana_fullstop 0x4a1
#define GDK_kana_openingbracket 0x4a2
#define GDK_kana_closingbracket 0x4a3
#define GDK_kana_comma 0x4a4
#define GDK_kana_conjunctive 0x4a5
#define GDK_kana_middledot 0x4a5
#define GDK_kana_WO 0x4a6
#define GDK_kana_a 0x4a7
#define GDK_kana_i 0x4a8
#define GDK_kana_u 0x4a9
#define GDK_kana_e 0x4aa
#define GDK_kana_o 0x4ab
#define GDK_kana_ya 0x4ac
#define GDK_kana_yu 0x4ad
#define GDK_kana_yo 0x4ae
#define GDK_kana_tsu 0x4af
#define GDK_kana_tu 0x4af
#define GDK_prolongedsound 0x4b0
#define GDK_kana_A 0x4b1
#define GDK_kana_I 0x4b2
#define GDK_kana_U 0x4b3
#define GDK_kana_E 0x4b4
#define GDK_kana_O 0x4b5
#define GDK_kana_KA 0x4b6
#define GDK_kana_KI 0x4b7
#define GDK_kana_KU 0x4b8
#define GDK_kana_KE 0x4b9
#define GDK_kana_KO 0x4ba
#define GDK_kana_SA 0x4bb
#define GDK_kana_SHI 0x4bc
#define GDK_kana_SU 0x4bd
#define GDK_kana_SE 0x4be
#define GDK_kana_SO 0x4bf
#define GDK_kana_TA 0x4c0
#define GDK_kana_CHI 0x4c1
#define GDK_kana_TI 0x4c1
#define GDK_kana_TSU 0x4c2
#define GDK_kana_TU 0x4c2
#define GDK_kana_TE 0x4c3
#define GDK_kana_TO 0x4c4
#define GDK_kana_NA 0x4c5
#define GDK_kana_NI 0x4c6
#define GDK_kana_NU 0x4c7
#define GDK_kana_NE 0x4c8
#define GDK_kana_NO 0x4c9
#define GDK_kana_HA 0x4ca
#define GDK_kana_HI 0x4cb
#define GDK_kana_FU 0x4cc
#define GDK_kana_HU 0x4cc
#define GDK_kana_HE 0x4cd
#define GDK_kana_HO 0x4ce
#define GDK_kana_MA 0x4cf
#define GDK_kana_MI 0x4d0
#define GDK_kana_MU 0x4d1
#define GDK_kana_ME 0x4d2
#define GDK_kana_MO 0x4d3
#define GDK_kana_YA 0x4d4
#define GDK_kana_YU 0x4d5
#define GDK_kana_YO 0x4d6
#define GDK_kana_RA 0x4d7
#define GDK_kana_RI 0x4d8
#define GDK_kana_RU 0x4d9
#define GDK_kana_RE 0x4da
#define GDK_kana_RO 0x4db
#define GDK_kana_WA 0x4dc
#define GDK_kana_N 0x4dd
#define GDK_voicedsound 0x4de
#define GDK_semivoicedsound 0x4df
#define GDK_kana_switch 0xff7e
#define GDK_Farsi_0 0x10006f0
#define GDK_Farsi_1 0x10006f1
#define GDK_Farsi_2 0x10006f2
#define GDK_Farsi_3 0x10006f3
#define GDK_Farsi_4 0x10006f4
#define GDK_Farsi_5 0x10006f5
#define GDK_Farsi_6 0x10006f6
#define GDK_Farsi_7 0x10006f7
#define GDK_Farsi_8 0x10006f8
#define GDK_Farsi_9 0x10006f9
#define GDK_Arabic_percent 0x100066a
#define GDK_Arabic_superscript_alef 0x1000670
#define GDK_Arabic_tteh 0x1000679
#define GDK_Arabic_peh 0x100067e
#define GDK_Arabic_tcheh 0x1000686
#define GDK_Arabic_ddal 0x1000688
#define GDK_Arabic_rreh 0x1000691
#define GDK_Arabic_comma 0x5ac
#define GDK_Arabic_fullstop 0x10006d4
#define GDK_Arabic_0 0x1000660
#define GDK_Arabic_1 0x1000661
#define GDK_Arabic_2 0x1000662
#define GDK_Arabic_3 0x1000663
#define GDK_Arabic_4 0x1000664
#define GDK_Arabic_5 0x1000665
#define GDK_Arabic_6 0x1000666
#define GDK_Arabic_7 0x1000667
#define GDK_Arabic_8 0x1000668
#define GDK_Arabic_9 0x1000669
#define GDK_Arabic_semicolon 0x5bb
#define GDK_Arabic_question_mark 0x5bf
#define GDK_Arabic_hamza 0x5c1
#define GDK_Arabic_maddaonalef 0x5c2
#define GDK_Arabic_hamzaonalef 0x5c3
#define GDK_Arabic_hamzaonwaw 0x5c4
#define GDK_Arabic_hamzaunderalef 0x5c5
#define GDK_Arabic_hamzaonyeh 0x5c6
#define GDK_Arabic_alef 0x5c7
#define GDK_Arabic_beh 0x5c8
#define GDK_Arabic_tehmarbuta 0x5c9
#define GDK_Arabic_teh 0x5ca
#define GDK_Arabic_theh 0x5cb
#define GDK_Arabic_jeem 0x5cc
#define GDK_Arabic_hah 0x5cd
#define GDK_Arabic_khah 0x5ce
#define GDK_Arabic_dal 0x5cf
#define GDK_Arabic_thal 0x5d0
#define GDK_Arabic_ra 0x5d1
#define GDK_Arabic_zain 0x5d2
#define GDK_Arabic_seen 0x5d3
#define GDK_Arabic_sheen 0x5d4
#define GDK_Arabic_sad 0x5d5
#define GDK_Arabic_dad 0x5d6
#define GDK_Arabic_tah 0x5d7
#define GDK_Arabic_zah 0x5d8
#define GDK_Arabic_ain 0x5d9
#define GDK_Arabic_ghain 0x5da
#define GDK_Arabic_tatweel 0x5e0
#define GDK_Arabic_feh 0x5e1
#define GDK_Arabic_qaf 0x5e2
#define GDK_Arabic_kaf 0x5e3
#define GDK_Arabic_lam 0x5e4
#define GDK_Arabic_meem 0x5e5
#define GDK_Arabic_noon 0x5e6
#define GDK_Arabic_ha 0x5e7
#define GDK_Arabic_heh 0x5e7
#define GDK_Arabic_waw 0x5e8
#define GDK_Arabic_alefmaksura 0x5e9
#define GDK_Arabic_yeh 0x5ea
#define GDK_Arabic_fathatan 0x5eb
#define GDK_Arabic_dammatan 0x5ec
#define GDK_Arabic_kasratan 0x5ed
#define GDK_Arabic_fatha 0x5ee
#define GDK_Arabic_damma 0x5ef
#define GDK_Arabic_kasra 0x5f0
#define GDK_Arabic_shadda 0x5f1
#define GDK_Arabic_sukun 0x5f2
#define GDK_Arabic_madda_above 0x1000653
#define GDK_Arabic_hamza_above 0x1000654
#define GDK_Arabic_hamza_below 0x1000655
#define GDK_Arabic_jeh 0x1000698
#define GDK_Arabic_veh 0x10006a4
#define GDK_Arabic_keheh 0x10006a9
#define GDK_Arabic_gaf 0x10006af
#define GDK_Arabic_noon_ghunna 0x10006ba
#define GDK_Arabic_heh_doachashmee 0x10006be
#define GDK_Farsi_yeh 0x10006cc
#define GDK_Arabic_farsi_yeh 0x10006cc
#define GDK_Arabic_yeh_baree 0x10006d2
#define GDK_Arabic_heh_goal 0x10006c1
#define GDK_Arabic_switch 0xff7e
#define GDK_Cyrillic_GHE_bar 0x1000492
#define GDK_Cyrillic_ghe_bar 0x1000493
#define GDK_Cyrillic_ZHE_descender 0x1000496
#define GDK_Cyrillic_zhe_descender 0x1000497
#define GDK_Cyrillic_KA_descender 0x100049a
#define GDK_Cyrillic_ka_descender 0x100049b
#define GDK_Cyrillic_KA_vertstroke 0x100049c
#define GDK_Cyrillic_ka_vertstroke 0x100049d
#define GDK_Cyrillic_EN_descender 0x10004a2
#define GDK_Cyrillic_en_descender 0x10004a3
#define GDK_Cyrillic_U_straight 0x10004ae
#define GDK_Cyrillic_u_straight 0x10004af
#define GDK_Cyrillic_U_straight_bar 0x10004b0
#define GDK_Cyrillic_u_straight_bar 0x10004b1
#define GDK_Cyrillic_HA_descender 0x10004b2
#define GDK_Cyrillic_ha_descender 0x10004b3
#define GDK_Cyrillic_CHE_descender 0x10004b6
#define GDK_Cyrillic_che_descender 0x10004b7
#define GDK_Cyrillic_CHE_vertstroke 0x10004b8
#define GDK_Cyrillic_che_vertstroke 0x10004b9
#define GDK_Cyrillic_SHHA 0x10004ba
#define GDK_Cyrillic_shha 0x10004bb
#define GDK_Cyrillic_SCHWA 0x10004d8
#define GDK_Cyrillic_schwa 0x10004d9
#define GDK_Cyrillic_I_macron 0x10004e2
#define GDK_Cyrillic_i_macron 0x10004e3
#define GDK_Cyrillic_O_bar 0x10004e8
#define GDK_Cyrillic_o_bar 0x10004e9
#define GDK_Cyrillic_U_macron 0x10004ee
#define GDK_Cyrillic_u_macron 0x10004ef
#define GDK_Serbian_dje 0x6a1
#define GDK_Macedonia_gje 0x6a2
#define GDK_Cyrillic_io 0x6a3
#define GDK_Ukrainian_ie 0x6a4
#define GDK_Ukranian_je 0x6a4
#define GDK_Macedonia_dse 0x6a5
#define GDK_Ukrainian_i 0x6a6
#define GDK_Ukranian_i 0x6a6
#define GDK_Ukrainian_yi 0x6a7
#define GDK_Ukranian_yi 0x6a7
#define GDK_Cyrillic_je 0x6a8
#define GDK_Serbian_je 0x6a8
#define GDK_Cyrillic_lje 0x6a9
#define GDK_Serbian_lje 0x6a9
#define GDK_Cyrillic_nje 0x6aa
#define GDK_Serbian_nje 0x6aa
#define GDK_Serbian_tshe 0x6ab
#define GDK_Macedonia_kje 0x6ac
#define GDK_Ukrainian_ghe_with_upturn 0x6ad
#define GDK_Byelorussian_shortu 0x6ae
#define GDK_Cyrillic_dzhe 0x6af
#define GDK_Serbian_dze 0x6af
#define GDK_numerosign 0x6b0
#define GDK_Serbian_DJE 0x6b1
#define GDK_Macedonia_GJE 0x6b2
#define GDK_Cyrillic_IO 0x6b3
#define GDK_Ukrainian_IE 0x6b4
#define GDK_Ukranian_JE 0x6b4
#define GDK_Macedonia_DSE 0x6b5
#define GDK_Ukrainian_I 0x6b6
#define GDK_Ukranian_I 0x6b6
#define GDK_Ukrainian_YI 0x6b7
#define GDK_Ukranian_YI 0x6b7
#define GDK_Cyrillic_JE 0x6b8
#define GDK_Serbian_JE 0x6b8
#define GDK_Cyrillic_LJE 0x6b9
#define GDK_Serbian_LJE 0x6b9
#define GDK_Cyrillic_NJE 0x6ba
#define GDK_Serbian_NJE 0x6ba
#define GDK_Serbian_TSHE 0x6bb
#define GDK_Macedonia_KJE 0x6bc
#define GDK_Ukrainian_GHE_WITH_UPTURN 0x6bd
#define GDK_Byelorussian_SHORTU 0x6be
#define GDK_Cyrillic_DZHE 0x6bf
#define GDK_Serbian_DZE 0x6bf
#define GDK_Cyrillic_yu 0x6c0
#define GDK_Cyrillic_a 0x6c1
#define GDK_Cyrillic_be 0x6c2
#define GDK_Cyrillic_tse 0x6c3
#define GDK_Cyrillic_de 0x6c4
#define GDK_Cyrillic_ie 0x6c5
#define GDK_Cyrillic_ef 0x6c6
#define GDK_Cyrillic_ghe 0x6c7
#define GDK_Cyrillic_ha 0x6c8
#define GDK_Cyrillic_i 0x6c9
#define GDK_Cyrillic_shorti 0x6ca
#define GDK_Cyrillic_ka 0x6cb
#define GDK_Cyrillic_el 0x6cc
#define GDK_Cyrillic_em 0x6cd
#define GDK_Cyrillic_en 0x6ce
#define GDK_Cyrillic_o 0x6cf
#define GDK_Cyrillic_pe 0x6d0
#define GDK_Cyrillic_ya 0x6d1
#define GDK_Cyrillic_er 0x6d2
#define GDK_Cyrillic_es 0x6d3
#define GDK_Cyrillic_te 0x6d4
#define GDK_Cyrillic_u 0x6d5
#define GDK_Cyrillic_zhe 0x6d6
#define GDK_Cyrillic_ve 0x6d7
#define GDK_Cyrillic_softsign 0x6d8
#define GDK_Cyrillic_yeru 0x6d9
#define GDK_Cyrillic_ze 0x6da
#define GDK_Cyrillic_sha 0x6db
#define GDK_Cyrillic_e 0x6dc
#define GDK_Cyrillic_shcha 0x6dd
#define GDK_Cyrillic_che 0x6de
#define GDK_Cyrillic_hardsign 0x6df
#define GDK_Cyrillic_YU 0x6e0
#define GDK_Cyrillic_A 0x6e1
#define GDK_Cyrillic_BE 0x6e2
#define GDK_Cyrillic_TSE 0x6e3
#define GDK_Cyrillic_DE 0x6e4
#define GDK_Cyrillic_IE 0x6e5
#define GDK_Cyrillic_EF 0x6e6
#define GDK_Cyrillic_GHE 0x6e7
#define GDK_Cyrillic_HA 0x6e8
#define GDK_Cyrillic_I 0x6e9
#define GDK_Cyrillic_SHORTI 0x6ea
#define GDK_Cyrillic_KA 0x6eb
#define GDK_Cyrillic_EL 0x6ec
#define GDK_Cyrillic_EM 0x6ed
#define GDK_Cyrillic_EN 0x6ee
#define GDK_Cyrillic_O 0x6ef
#define GDK_Cyrillic_PE 0x6f0
#define GDK_Cyrillic_YA 0x6f1
#define GDK_Cyrillic_ER 0x6f2
#define GDK_Cyrillic_ES 0x6f3
#define GDK_Cyrillic_TE 0x6f4
#define GDK_Cyrillic_U 0x6f5
#define GDK_Cyrillic_ZHE 0x6f6
#define GDK_Cyrillic_VE 0x6f7
#define GDK_Cyrillic_SOFTSIGN 0x6f8
#define GDK_Cyrillic_YERU 0x6f9
#define GDK_Cyrillic_ZE 0x6fa
#define GDK_Cyrillic_SHA 0x6fb
#define GDK_Cyrillic_E 0x6fc
#define GDK_Cyrillic_SHCHA 0x6fd
#define GDK_Cyrillic_CHE 0x6fe
#define GDK_Cyrillic_HARDSIGN 0x6ff
#define GDK_Greek_ALPHAaccent 0x7a1
#define GDK_Greek_EPSILONaccent 0x7a2
#define GDK_Greek_ETAaccent 0x7a3
#define GDK_Greek_IOTAaccent 0x7a4
#define GDK_Greek_IOTAdieresis 0x7a5
#define GDK_Greek_IOTAdiaeresis 0x7a5
#define GDK_Greek_OMICRONaccent 0x7a7
#define GDK_Greek_UPSILONaccent 0x7a8
#define GDK_Greek_UPSILONdieresis 0x7a9
#define GDK_Greek_OMEGAaccent 0x7ab
#define GDK_Greek_accentdieresis 0x7ae
#define GDK_Greek_horizbar 0x7af
#define GDK_Greek_alphaaccent 0x7b1
#define GDK_Greek_epsilonaccent 0x7b2
#define GDK_Greek_etaaccent 0x7b3
#define GDK_Greek_iotaaccent 0x7b4
#define GDK_Greek_iotadieresis 0x7b5
#define GDK_Greek_iotaaccentdieresis 0x7b6
#define GDK_Greek_omicronaccent 0x7b7
#define GDK_Greek_upsilonaccent 0x7b8
#define GDK_Greek_upsilondieresis 0x7b9
#define GDK_Greek_upsilonaccentdieresis 0x7ba
#define GDK_Greek_omegaaccent 0x7bb
#define GDK_Greek_ALPHA 0x7c1
#define GDK_Greek_BETA 0x7c2
#define GDK_Greek_GAMMA 0x7c3
#define GDK_Greek_DELTA 0x7c4
#define GDK_Greek_EPSILON 0x7c5
#define GDK_Greek_ZETA 0x7c6
#define GDK_Greek_ETA 0x7c7
#define GDK_Greek_THETA 0x7c8
#define GDK_Greek_IOTA 0x7c9
#define GDK_Greek_KAPPA 0x7ca
#define GDK_Greek_LAMDA 0x7cb
#define GDK_Greek_LAMBDA 0x7cb
#define GDK_Greek_MU 0x7cc
#define GDK_Greek_NU 0x7cd
#define GDK_Greek_XI 0x7ce
#define GDK_Greek_OMICRON 0x7cf
#define GDK_Greek_PI 0x7d0
#define GDK_Greek_RHO 0x7d1
#define GDK_Greek_SIGMA 0x7d2
#define GDK_Greek_TAU 0x7d4
#define GDK_Greek_UPSILON 0x7d5
#define GDK_Greek_PHI 0x7d6
#define GDK_Greek_CHI 0x7d7
#define GDK_Greek_PSI 0x7d8
#define GDK_Greek_OMEGA 0x7d9
#define GDK_Greek_alpha 0x7e1
#define GDK_Greek_beta 0x7e2
#define GDK_Greek_gamma 0x7e3
#define GDK_Greek_delta 0x7e4
#define GDK_Greek_epsilon 0x7e5
#define GDK_Greek_zeta 0x7e6
#define GDK_Greek_eta 0x7e7
#define GDK_Greek_theta 0x7e8
#define GDK_Greek_iota 0x7e9
#define GDK_Greek_kappa 0x7ea
#define GDK_Greek_lamda 0x7eb
#define GDK_Greek_lambda 0x7eb
#define GDK_Greek_mu 0x7ec
#define GDK_Greek_nu 0x7ed
#define GDK_Greek_xi 0x7ee
#define GDK_Greek_omicron 0x7ef
#define GDK_Greek_pi 0x7f0
#define GDK_Greek_rho 0x7f1
#define GDK_Greek_sigma 0x7f2
#define GDK_Greek_finalsmallsigma 0x7f3
#define GDK_Greek_tau 0x7f4
#define GDK_Greek_upsilon 0x7f5
#define GDK_Greek_phi 0x7f6
#define GDK_Greek_chi 0x7f7
#define GDK_Greek_psi 0x7f8
#define GDK_Greek_omega 0x7f9
#define GDK_Greek_switch 0xff7e
#define GDK_leftradical 0x8a1
#define GDK_topleftradical 0x8a2
#define GDK_horizconnector 0x8a3
#define GDK_topintegral 0x8a4
#define GDK_botintegral 0x8a5
#define GDK_vertconnector 0x8a6
#define GDK_topleftsqbracket 0x8a7
#define GDK_botleftsqbracket 0x8a8
#define GDK_toprightsqbracket 0x8a9
#define GDK_botrightsqbracket 0x8aa
#define GDK_topleftparens 0x8ab
#define GDK_botleftparens 0x8ac
#define GDK_toprightparens 0x8ad
#define GDK_botrightparens 0x8ae
#define GDK_leftmiddlecurlybrace 0x8af
#define GDK_rightmiddlecurlybrace 0x8b0
#define GDK_topleftsummation 0x8b1
#define GDK_botleftsummation 0x8b2
#define GDK_topvertsummationconnector 0x8b3
#define GDK_botvertsummationconnector 0x8b4
#define GDK_toprightsummation 0x8b5
#define GDK_botrightsummation 0x8b6
#define GDK_rightmiddlesummation 0x8b7
#define GDK_lessthanequal 0x8bc
#define GDK_notequal 0x8bd
#define GDK_greaterthanequal 0x8be
#define GDK_integral 0x8bf
#define GDK_therefore 0x8c0
#define GDK_variation 0x8c1
#define GDK_infinity 0x8c2
#define GDK_nabla 0x8c5
#define GDK_approximate 0x8c8
#define GDK_similarequal 0x8c9
#define GDK_ifonlyif 0x8cd
#define GDK_implies 0x8ce
#define GDK_identical 0x8cf
#define GDK_radical 0x8d6
#define GDK_includedin 0x8da
#define GDK_includes 0x8db
#define GDK_intersection 0x8dc
#define GDK_union 0x8dd
#define GDK_logicaland 0x8de
#define GDK_logicalor 0x8df
#define GDK_partialderivative 0x8ef
#define GDK_function 0x8f6
#define GDK_leftarrow 0x8fb
#define GDK_uparrow 0x8fc
#define GDK_rightarrow 0x8fd
#define GDK_downarrow 0x8fe
#define GDK_blank 0x9df
#define GDK_soliddiamond 0x9e0
#define GDK_checkerboard 0x9e1
#define GDK_ht 0x9e2
#define GDK_ff 0x9e3
#define GDK_cr 0x9e4
#define GDK_lf 0x9e5
#define GDK_nl 0x9e8
#define GDK_vt 0x9e9
#define GDK_lowrightcorner 0x9ea
#define GDK_uprightcorner 0x9eb
#define GDK_upleftcorner 0x9ec
#define GDK_lowleftcorner 0x9ed
#define GDK_crossinglines 0x9ee
#define GDK_horizlinescan1 0x9ef
#define GDK_horizlinescan3 0x9f0
#define GDK_horizlinescan5 0x9f1
#define GDK_horizlinescan7 0x9f2
#define GDK_horizlinescan9 0x9f3
#define GDK_leftt 0x9f4
#define GDK_rightt 0x9f5
#define GDK_bott 0x9f6
#define GDK_topt 0x9f7
#define GDK_vertbar 0x9f8
#define GDK_emspace 0xaa1
#define GDK_enspace 0xaa2
#define GDK_em3space 0xaa3
#define GDK_em4space 0xaa4
#define GDK_digitspace 0xaa5
#define GDK_punctspace 0xaa6
#define GDK_thinspace 0xaa7
#define GDK_hairspace 0xaa8
#define GDK_emdash 0xaa9
#define GDK_endash 0xaaa
#define GDK_signifblank 0xaac
#define GDK_ellipsis 0xaae
#define GDK_doubbaselinedot 0xaaf
#define GDK_onethird 0xab0
#define GDK_twothirds 0xab1
#define GDK_onefifth 0xab2
#define GDK_twofifths 0xab3
#define GDK_threefifths 0xab4
#define GDK_fourfifths 0xab5
#define GDK_onesixth 0xab6
#define GDK_fivesixths 0xab7
#define GDK_careof 0xab8
#define GDK_figdash 0xabb
#define GDK_leftanglebracket 0xabc
#define GDK_decimalpoint 0xabd
#define GDK_rightanglebracket 0xabe
#define GDK_marker 0xabf
#define GDK_oneeighth 0xac3
#define GDK_threeeighths 0xac4
#define GDK_fiveeighths 0xac5
#define GDK_seveneighths 0xac6
#define GDK_trademark 0xac9
#define GDK_signaturemark 0xaca
#define GDK_trademarkincircle 0xacb
#define GDK_leftopentriangle 0xacc
#define GDK_rightopentriangle 0xacd
#define GDK_emopencircle 0xace
#define GDK_emopenrectangle 0xacf
#define GDK_leftsinglequotemark 0xad0
#define GDK_rightsinglequotemark 0xad1
#define GDK_leftdoublequotemark 0xad2
#define GDK_rightdoublequotemark 0xad3
#define GDK_prescription 0xad4
#define GDK_minutes 0xad6
#define GDK_seconds 0xad7
#define GDK_latincross 0xad9
#define GDK_hexagram 0xada
#define GDK_filledrectbullet 0xadb
#define GDK_filledlefttribullet 0xadc
#define GDK_filledrighttribullet 0xadd
#define GDK_emfilledcircle 0xade
#define GDK_emfilledrect 0xadf
#define GDK_enopencircbullet 0xae0
#define GDK_enopensquarebullet 0xae1
#define GDK_openrectbullet 0xae2
#define GDK_opentribulletup 0xae3
#define GDK_opentribulletdown 0xae4
#define GDK_openstar 0xae5
#define GDK_enfilledcircbullet 0xae6
#define GDK_enfilledsqbullet 0xae7
#define GDK_filledtribulletup 0xae8
#define GDK_filledtribulletdown 0xae9
#define GDK_leftpointer 0xaea
#define GDK_rightpointer 0xaeb
#define GDK_club 0xaec
#define GDK_diamond 0xaed
#define GDK_heart 0xaee
#define GDK_maltesecross 0xaf0
#define GDK_dagger 0xaf1
#define GDK_doubledagger 0xaf2
#define GDK_checkmark 0xaf3
#define GDK_ballotcross 0xaf4
#define GDK_musicalsharp 0xaf5
#define GDK_musicalflat 0xaf6
#define GDK_malesymbol 0xaf7
#define GDK_femalesymbol 0xaf8
#define GDK_telephone 0xaf9
#define GDK_telephonerecorder 0xafa
#define GDK_phonographcopyright 0xafb
#define GDK_caret 0xafc
#define GDK_singlelowquotemark 0xafd
#define GDK_doublelowquotemark 0xafe
#define GDK_cursor 0xaff
#define GDK_leftcaret 0xba3
#define GDK_rightcaret 0xba6
#define GDK_downcaret 0xba8
#define GDK_upcaret 0xba9
#define GDK_overbar 0xbc0
#define GDK_downtack 0xbc2
#define GDK_upshoe 0xbc3
#define GDK_downstile 0xbc4
#define GDK_underbar 0xbc6
#define GDK_jot 0xbca
#define GDK_quad 0xbcc
#define GDK_uptack 0xbce
#define GDK_circle 0xbcf
#define GDK_upstile 0xbd3
#define GDK_downshoe 0xbd6
#define GDK_rightshoe 0xbd8
#define GDK_leftshoe 0xbda
#define GDK_lefttack 0xbdc
#define GDK_righttack 0xbfc
#define GDK_hebrew_doublelowline 0xcdf
#define GDK_hebrew_aleph 0xce0
#define GDK_hebrew_bet 0xce1
#define GDK_hebrew_beth 0xce1
#define GDK_hebrew_gimel 0xce2
#define GDK_hebrew_gimmel 0xce2
#define GDK_hebrew_dalet 0xce3
#define GDK_hebrew_daleth 0xce3
#define GDK_hebrew_he 0xce4
#define GDK_hebrew_waw 0xce5
#define GDK_hebrew_zain 0xce6
#define GDK_hebrew_zayin 0xce6
#define GDK_hebrew_chet 0xce7
#define GDK_hebrew_het 0xce7
#define GDK_hebrew_tet 0xce8
#define GDK_hebrew_teth 0xce8
#define GDK_hebrew_yod 0xce9
#define GDK_hebrew_finalkaph 0xcea
#define GDK_hebrew_kaph 0xceb
#define GDK_hebrew_lamed 0xcec
#define GDK_hebrew_finalmem 0xced
#define GDK_hebrew_mem 0xcee
#define GDK_hebrew_finalnun 0xcef
#define GDK_hebrew_nun 0xcf0
#define GDK_hebrew_samech 0xcf1
#define GDK_hebrew_samekh 0xcf1
#define GDK_hebrew_ayin 0xcf2
#define GDK_hebrew_finalpe 0xcf3
#define GDK_hebrew_pe 0xcf4
#define GDK_hebrew_finalzade 0xcf5
#define GDK_hebrew_finalzadi 0xcf5
#define GDK_hebrew_zade 0xcf6
#define GDK_hebrew_zadi 0xcf6
#define GDK_hebrew_qoph 0xcf7
#define GDK_hebrew_kuf 0xcf7
#define GDK_hebrew_resh 0xcf8
#define GDK_hebrew_shin 0xcf9
#define GDK_hebrew_taw 0xcfa
#define GDK_hebrew_taf 0xcfa
#define GDK_Hebrew_switch 0xff7e
#define GDK_Thai_kokai 0xda1
#define GDK_Thai_khokhai 0xda2
#define GDK_Thai_khokhuat 0xda3
#define GDK_Thai_khokhwai 0xda4
#define GDK_Thai_khokhon 0xda5
#define GDK_Thai_khorakhang 0xda6
#define GDK_Thai_ngongu 0xda7
#define GDK_Thai_chochan 0xda8
#define GDK_Thai_choching 0xda9
#define GDK_Thai_chochang 0xdaa
#define GDK_Thai_soso 0xdab
#define GDK_Thai_chochoe 0xdac
#define GDK_Thai_yoying 0xdad
#define GDK_Thai_dochada 0xdae
#define GDK_Thai_topatak 0xdaf
#define GDK_Thai_thothan 0xdb0
#define GDK_Thai_thonangmontho 0xdb1
#define GDK_Thai_thophuthao 0xdb2
#define GDK_Thai_nonen 0xdb3
#define GDK_Thai_dodek 0xdb4
#define GDK_Thai_totao 0xdb5
#define GDK_Thai_thothung 0xdb6
#define GDK_Thai_thothahan 0xdb7
#define GDK_Thai_thothong 0xdb8
#define GDK_Thai_nonu 0xdb9
#define GDK_Thai_bobaimai 0xdba
#define GDK_Thai_popla 0xdbb
#define GDK_Thai_phophung 0xdbc
#define GDK_Thai_fofa 0xdbd
#define GDK_Thai_phophan 0xdbe
#define GDK_Thai_fofan 0xdbf
#define GDK_Thai_phosamphao 0xdc0
#define GDK_Thai_moma 0xdc1
#define GDK_Thai_yoyak 0xdc2
#define GDK_Thai_rorua 0xdc3
#define GDK_Thai_ru 0xdc4
#define GDK_Thai_loling 0xdc5
#define GDK_Thai_lu 0xdc6
#define GDK_Thai_wowaen 0xdc7
#define GDK_Thai_sosala 0xdc8
#define GDK_Thai_sorusi 0xdc9
#define GDK_Thai_sosua 0xdca
#define GDK_Thai_hohip 0xdcb
#define GDK_Thai_lochula 0xdcc
#define GDK_Thai_oang 0xdcd
#define GDK_Thai_honokhuk 0xdce
#define GDK_Thai_paiyannoi 0xdcf
#define GDK_Thai_saraa 0xdd0
#define GDK_Thai_maihanakat 0xdd1
#define GDK_Thai_saraaa 0xdd2
#define GDK_Thai_saraam 0xdd3
#define GDK_Thai_sarai 0xdd4
#define GDK_Thai_saraii 0xdd5
#define GDK_Thai_saraue 0xdd6
#define GDK_Thai_sarauee 0xdd7
#define GDK_Thai_sarau 0xdd8
#define GDK_Thai_sarauu 0xdd9
#define GDK_Thai_phinthu 0xdda
#define GDK_Thai_maihanakat_maitho 0xdde
#define GDK_Thai_baht 0xddf
#define GDK_Thai_sarae 0xde0
#define GDK_Thai_saraae 0xde1
#define GDK_Thai_sarao 0xde2
#define GDK_Thai_saraaimaimuan 0xde3
#define GDK_Thai_saraaimaimalai 0xde4
#define GDK_Thai_lakkhangyao 0xde5
#define GDK_Thai_maiyamok 0xde6
#define GDK_Thai_maitaikhu 0xde7
#define GDK_Thai_maiek 0xde8
#define GDK_Thai_maitho 0xde9
#define GDK_Thai_maitri 0xdea
#define GDK_Thai_maichattawa 0xdeb
#define GDK_Thai_thanthakhat 0xdec
#define GDK_Thai_nikhahit 0xded
#define GDK_Thai_leksun 0xdf0
#define GDK_Thai_leknung 0xdf1
#define GDK_Thai_leksong 0xdf2
#define GDK_Thai_leksam 0xdf3
#define GDK_Thai_leksi 0xdf4
#define GDK_Thai_lekha 0xdf5
#define GDK_Thai_lekhok 0xdf6
#define GDK_Thai_lekchet 0xdf7
#define GDK_Thai_lekpaet 0xdf8
#define GDK_Thai_lekkao 0xdf9
#define GDK_Hangul 0xff31
#define GDK_Hangul_Start 0xff32
#define GDK_Hangul_End 0xff33
#define GDK_Hangul_Hanja 0xff34
#define GDK_Hangul_Jamo 0xff35
#define GDK_Hangul_Romaja 0xff36
#define GDK_Hangul_Codeinput 0xff37
#define GDK_Hangul_Jeonja 0xff38
#define GDK_Hangul_Banja 0xff39
#define GDK_Hangul_PreHanja 0xff3a
#define GDK_Hangul_PostHanja 0xff3b
#define GDK_Hangul_SingleCandidate 0xff3c
#define GDK_Hangul_MultipleCandidate 0xff3d
#define GDK_Hangul_PreviousCandidate 0xff3e
#define GDK_Hangul_Special 0xff3f
#define GDK_Hangul_switch 0xff7e
#define GDK_Hangul_Kiyeog 0xea1
#define GDK_Hangul_SsangKiyeog 0xea2
#define GDK_Hangul_KiyeogSios 0xea3
#define GDK_Hangul_Nieun 0xea4
#define GDK_Hangul_NieunJieuj 0xea5
#define GDK_Hangul_NieunHieuh 0xea6
#define GDK_Hangul_Dikeud 0xea7
#define GDK_Hangul_SsangDikeud 0xea8
#define GDK_Hangul_Rieul 0xea9
#define GDK_Hangul_RieulKiyeog 0xeaa
#define GDK_Hangul_RieulMieum 0xeab
#define GDK_Hangul_RieulPieub 0xeac
#define GDK_Hangul_RieulSios 0xead
#define GDK_Hangul_RieulTieut 0xeae
#define GDK_Hangul_RieulPhieuf 0xeaf
#define GDK_Hangul_RieulHieuh 0xeb0
#define GDK_Hangul_Mieum 0xeb1
#define GDK_Hangul_Pieub 0xeb2
#define GDK_Hangul_SsangPieub 0xeb3
#define GDK_Hangul_PieubSios 0xeb4
#define GDK_Hangul_Sios 0xeb5
#define GDK_Hangul_SsangSios 0xeb6
#define GDK_Hangul_Ieung 0xeb7
#define GDK_Hangul_Jieuj 0xeb8
#define GDK_Hangul_SsangJieuj 0xeb9
#define GDK_Hangul_Cieuc 0xeba
#define GDK_Hangul_Khieuq 0xebb
#define GDK_Hangul_Tieut 0xebc
#define GDK_Hangul_Phieuf 0xebd
#define GDK_Hangul_Hieuh 0xebe
#define GDK_Hangul_A 0xebf
#define GDK_Hangul_AE 0xec0
#define GDK_Hangul_YA 0xec1
#define GDK_Hangul_YAE 0xec2
#define GDK_Hangul_EO 0xec3
#define GDK_Hangul_E 0xec4
#define GDK_Hangul_YEO 0xec5
#define GDK_Hangul_YE 0xec6
#define GDK_Hangul_O 0xec7
#define GDK_Hangul_WA 0xec8
#define GDK_Hangul_WAE 0xec9
#define GDK_Hangul_OE 0xeca
#define GDK_Hangul_YO 0xecb
#define GDK_Hangul_U 0xecc
#define GDK_Hangul_WEO 0xecd
#define GDK_Hangul_WE 0xece
#define GDK_Hangul_WI 0xecf
#define GDK_Hangul_YU 0xed0
#define GDK_Hangul_EU 0xed1
#define GDK_Hangul_YI 0xed2
#define GDK_Hangul_I 0xed3
#define GDK_Hangul_J_Kiyeog 0xed4
#define GDK_Hangul_J_SsangKiyeog 0xed5
#define GDK_Hangul_J_KiyeogSios 0xed6
#define GDK_Hangul_J_Nieun 0xed7
#define GDK_Hangul_J_NieunJieuj 0xed8
#define GDK_Hangul_J_NieunHieuh 0xed9
#define GDK_Hangul_J_Dikeud 0xeda
#define GDK_Hangul_J_Rieul 0xedb
#define GDK_Hangul_J_RieulKiyeog 0xedc
#define GDK_Hangul_J_RieulMieum 0xedd
#define GDK_Hangul_J_RieulPieub 0xede
#define GDK_Hangul_J_RieulSios 0xedf
#define GDK_Hangul_J_RieulTieut 0xee0
#define GDK_Hangul_J_RieulPhieuf 0xee1
#define GDK_Hangul_J_RieulHieuh 0xee2
#define GDK_Hangul_J_Mieum 0xee3
#define GDK_Hangul_J_Pieub 0xee4
#define GDK_Hangul_J_PieubSios 0xee5
#define GDK_Hangul_J_Sios 0xee6
#define GDK_Hangul_J_SsangSios 0xee7
#define GDK_Hangul_J_Ieung 0xee8
#define GDK_Hangul_J_Jieuj 0xee9
#define GDK_Hangul_J_Cieuc 0xeea
#define GDK_Hangul_J_Khieuq 0xeeb
#define GDK_Hangul_J_Tieut 0xeec
#define GDK_Hangul_J_Phieuf 0xeed
#define GDK_Hangul_J_Hieuh 0xeee
#define GDK_Hangul_RieulYeorinHieuh 0xeef
#define GDK_Hangul_SunkyeongeumMieum 0xef0
#define GDK_Hangul_SunkyeongeumPieub 0xef1
#define GDK_Hangul_PanSios 0xef2
#define GDK_Hangul_KkogjiDalrinIeung 0xef3
#define GDK_Hangul_SunkyeongeumPhieuf 0xef4
#define GDK_Hangul_YeorinHieuh 0xef5
#define GDK_Hangul_AraeA 0xef6
#define GDK_Hangul_AraeAE 0xef7
#define GDK_Hangul_J_PanSios 0xef8
#define GDK_Hangul_J_KkogjiDalrinIeung 0xef9
#define GDK_Hangul_J_YeorinHieuh 0xefa
#define GDK_Korean_Won 0xeff
#define GDK_Armenian_ligature_ew 0x1000587
#define GDK_Armenian_full_stop 0x1000589
#define GDK_Armenian_verjaket 0x1000589
#define GDK_Armenian_separation_mark 0x100055d
#define GDK_Armenian_but 0x100055d
#define GDK_Armenian_hyphen 0x100058a
#define GDK_Armenian_yentamna 0x100058a
#define GDK_Armenian_exclam 0x100055c
#define GDK_Armenian_amanak 0x100055c
#define GDK_Armenian_accent 0x100055b
#define GDK_Armenian_shesht 0x100055b
#define GDK_Armenian_question 0x100055e
#define GDK_Armenian_paruyk 0x100055e
#define GDK_Armenian_AYB 0x1000531
#define GDK_Armenian_ayb 0x1000561
#define GDK_Armenian_BEN 0x1000532
#define GDK_Armenian_ben 0x1000562
#define GDK_Armenian_GIM 0x1000533
#define GDK_Armenian_gim 0x1000563
#define GDK_Armenian_DA 0x1000534
#define GDK_Armenian_da 0x1000564
#define GDK_Armenian_YECH 0x1000535
#define GDK_Armenian_yech 0x1000565
#define GDK_Armenian_ZA 0x1000536
#define GDK_Armenian_za 0x1000566
#define GDK_Armenian_E 0x1000537
#define GDK_Armenian_e 0x1000567
#define GDK_Armenian_AT 0x1000538
#define GDK_Armenian_at 0x1000568
#define GDK_Armenian_TO 0x1000539
#define GDK_Armenian_to 0x1000569
#define GDK_Armenian_ZHE 0x100053a
#define GDK_Armenian_zhe 0x100056a
#define GDK_Armenian_INI 0x100053b
#define GDK_Armenian_ini 0x100056b
#define GDK_Armenian_LYUN 0x100053c
#define GDK_Armenian_lyun 0x100056c
#define GDK_Armenian_KHE 0x100053d
#define GDK_Armenian_khe 0x100056d
#define GDK_Armenian_TSA 0x100053e
#define GDK_Armenian_tsa 0x100056e
#define GDK_Armenian_KEN 0x100053f
#define GDK_Armenian_ken 0x100056f
#define GDK_Armenian_HO 0x1000540
#define GDK_Armenian_ho 0x1000570
#define GDK_Armenian_DZA 0x1000541
#define GDK_Armenian_dza 0x1000571
#define GDK_Armenian_GHAT 0x1000542
#define GDK_Armenian_ghat 0x1000572
#define GDK_Armenian_TCHE 0x1000543
#define GDK_Armenian_tche 0x1000573
#define GDK_Armenian_MEN 0x1000544
#define GDK_Armenian_men 0x1000574
#define GDK_Armenian_HI 0x1000545
#define GDK_Armenian_hi 0x1000575
#define GDK_Armenian_NU 0x1000546
#define GDK_Armenian_nu 0x1000576
#define GDK_Armenian_SHA 0x1000547
#define GDK_Armenian_sha 0x1000577
#define GDK_Armenian_VO 0x1000548
#define GDK_Armenian_vo 0x1000578
#define GDK_Armenian_CHA 0x1000549
#define GDK_Armenian_cha 0x1000579
#define GDK_Armenian_PE 0x100054a
#define GDK_Armenian_pe 0x100057a
#define GDK_Armenian_JE 0x100054b
#define GDK_Armenian_je 0x100057b
#define GDK_Armenian_RA 0x100054c
#define GDK_Armenian_ra 0x100057c
#define GDK_Armenian_SE 0x100054d
#define GDK_Armenian_se 0x100057d
#define GDK_Armenian_VEV 0x100054e
#define GDK_Armenian_vev 0x100057e
#define GDK_Armenian_TYUN 0x100054f
#define GDK_Armenian_tyun 0x100057f
#define GDK_Armenian_RE 0x1000550
#define GDK_Armenian_re 0x1000580
#define GDK_Armenian_TSO 0x1000551
#define GDK_Armenian_tso 0x1000581
#define GDK_Armenian_VYUN 0x1000552
#define GDK_Armenian_vyun 0x1000582
#define GDK_Armenian_PYUR 0x1000553
#define GDK_Armenian_pyur 0x1000583
#define GDK_Armenian_KE 0x1000554
#define GDK_Armenian_ke 0x1000584
#define GDK_Armenian_O 0x1000555
#define GDK_Armenian_o 0x1000585
#define GDK_Armenian_FE 0x1000556
#define GDK_Armenian_fe 0x1000586
#define GDK_Armenian_apostrophe 0x100055a
#define GDK_Georgian_an 0x10010d0
#define GDK_Georgian_ban 0x10010d1
#define GDK_Georgian_gan 0x10010d2
#define GDK_Georgian_don 0x10010d3
#define GDK_Georgian_en 0x10010d4
#define GDK_Georgian_vin 0x10010d5
#define GDK_Georgian_zen 0x10010d6
#define GDK_Georgian_tan 0x10010d7
#define GDK_Georgian_in 0x10010d8
#define GDK_Georgian_kan 0x10010d9
#define GDK_Georgian_las 0x10010da
#define GDK_Georgian_man 0x10010db
#define GDK_Georgian_nar 0x10010dc
#define GDK_Georgian_on 0x10010dd
#define GDK_Georgian_par 0x10010de
#define GDK_Georgian_zhar 0x10010df
#define GDK_Georgian_rae 0x10010e0
#define GDK_Georgian_san 0x10010e1
#define GDK_Georgian_tar 0x10010e2
#define GDK_Georgian_un 0x10010e3
#define GDK_Georgian_phar 0x10010e4
#define GDK_Georgian_khar 0x10010e5
#define GDK_Georgian_ghan 0x10010e6
#define GDK_Georgian_qar 0x10010e7
#define GDK_Georgian_shin 0x10010e8
#define GDK_Georgian_chin 0x10010e9
#define GDK_Georgian_can 0x10010ea
#define GDK_Georgian_jil 0x10010eb
#define GDK_Georgian_cil 0x10010ec
#define GDK_Georgian_char 0x10010ed
#define GDK_Georgian_xan 0x10010ee
#define GDK_Georgian_jhan 0x10010ef
#define GDK_Georgian_hae 0x10010f0
#define GDK_Georgian_he 0x10010f1
#define GDK_Georgian_hie 0x10010f2
#define GDK_Georgian_we 0x10010f3
#define GDK_Georgian_har 0x10010f4
#define GDK_Georgian_hoe 0x10010f5
#define GDK_Georgian_fi 0x10010f6
#define GDK_Xabovedot 0x1001e8a
#define GDK_Ibreve 0x100012c
#define GDK_Zstroke 0x10001b5
#define GDK_Gcaron 0x10001e6
#define GDK_Ocaron 0x10001d1
#define GDK_Obarred 0x100019f
#define GDK_xabovedot 0x1001e8b
#define GDK_ibreve 0x100012d
#define GDK_zstroke 0x10001b6
#define GDK_gcaron 0x10001e7
#define GDK_ocaron 0x10001d2
#define GDK_obarred 0x1000275
#define GDK_SCHWA 0x100018f
#define GDK_schwa 0x1000259
#define GDK_Lbelowdot 0x1001e36
#define GDK_lbelowdot 0x1001e37
#define GDK_Abelowdot 0x1001ea0
#define GDK_abelowdot 0x1001ea1
#define GDK_Ahook 0x1001ea2
#define GDK_ahook 0x1001ea3
#define GDK_Acircumflexacute 0x1001ea4
#define GDK_acircumflexacute 0x1001ea5
#define GDK_Acircumflexgrave 0x1001ea6
#define GDK_acircumflexgrave 0x1001ea7
#define GDK_Acircumflexhook 0x1001ea8
#define GDK_acircumflexhook 0x1001ea9
#define GDK_Acircumflextilde 0x1001eaa
#define GDK_acircumflextilde 0x1001eab
#define GDK_Acircumflexbelowdot 0x1001eac
#define GDK_acircumflexbelowdot 0x1001ead
#define GDK_Abreveacute 0x1001eae
#define GDK_abreveacute 0x1001eaf
#define GDK_Abrevegrave 0x1001eb0
#define GDK_abrevegrave 0x1001eb1
#define GDK_Abrevehook 0x1001eb2
#define GDK_abrevehook 0x1001eb3
#define GDK_Abrevetilde 0x1001eb4
#define GDK_abrevetilde 0x1001eb5
#define GDK_Abrevebelowdot 0x1001eb6
#define GDK_abrevebelowdot 0x1001eb7
#define GDK_Ebelowdot 0x1001eb8
#define GDK_ebelowdot 0x1001eb9
#define GDK_Ehook 0x1001eba
#define GDK_ehook 0x1001ebb
#define GDK_Etilde 0x1001ebc
#define GDK_etilde 0x1001ebd
#define GDK_Ecircumflexacute 0x1001ebe
#define GDK_ecircumflexacute 0x1001ebf
#define GDK_Ecircumflexgrave 0x1001ec0
#define GDK_ecircumflexgrave 0x1001ec1
#define GDK_Ecircumflexhook 0x1001ec2
#define GDK_ecircumflexhook 0x1001ec3
#define GDK_Ecircumflextilde 0x1001ec4
#define GDK_ecircumflextilde 0x1001ec5
#define GDK_Ecircumflexbelowdot 0x1001ec6
#define GDK_ecircumflexbelowdot 0x1001ec7
#define GDK_Ihook 0x1001ec8
#define GDK_ihook 0x1001ec9
#define GDK_Ibelowdot 0x1001eca
#define GDK_ibelowdot 0x1001ecb
#define GDK_Obelowdot 0x1001ecc
#define GDK_obelowdot 0x1001ecd
#define GDK_Ohook 0x1001ece
#define GDK_ohook 0x1001ecf
#define GDK_Ocircumflexacute 0x1001ed0
#define GDK_ocircumflexacute 0x1001ed1
#define GDK_Ocircumflexgrave 0x1001ed2
#define GDK_ocircumflexgrave 0x1001ed3
#define GDK_Ocircumflexhook 0x1001ed4
#define GDK_ocircumflexhook 0x1001ed5
#define GDK_Ocircumflextilde 0x1001ed6
#define GDK_ocircumflextilde 0x1001ed7
#define GDK_Ocircumflexbelowdot 0x1001ed8
#define GDK_ocircumflexbelowdot 0x1001ed9
#define GDK_Ohornacute 0x1001eda
#define GDK_ohornacute 0x1001edb
#define GDK_Ohorngrave 0x1001edc
#define GDK_ohorngrave 0x1001edd
#define GDK_Ohornhook 0x1001ede
#define GDK_ohornhook 0x1001edf
#define GDK_Ohorntilde 0x1001ee0
#define GDK_ohorntilde 0x1001ee1
#define GDK_Ohornbelowdot 0x1001ee2
#define GDK_ohornbelowdot 0x1001ee3
#define GDK_Ubelowdot 0x1001ee4
#define GDK_ubelowdot 0x1001ee5
#define GDK_Uhook 0x1001ee6
#define GDK_uhook 0x1001ee7
#define GDK_Uhornacute 0x1001ee8
#define GDK_uhornacute 0x1001ee9
#define GDK_Uhorngrave 0x1001eea
#define GDK_uhorngrave 0x1001eeb
#define GDK_Uhornhook 0x1001eec
#define GDK_uhornhook 0x1001eed
#define GDK_Uhorntilde 0x1001eee
#define GDK_uhorntilde 0x1001eef
#define GDK_Uhornbelowdot 0x1001ef0
#define GDK_uhornbelowdot 0x1001ef1
#define GDK_Ybelowdot 0x1001ef4
#define GDK_ybelowdot 0x1001ef5
#define GDK_Yhook 0x1001ef6
#define GDK_yhook 0x1001ef7
#define GDK_Ytilde 0x1001ef8
#define GDK_ytilde 0x1001ef9
#define GDK_Ohorn 0x10001a0
#define GDK_ohorn 0x10001a1
#define GDK_Uhorn 0x10001af
#define GDK_uhorn 0x10001b0
#define GDK_EcuSign 0x10020a0
#define GDK_ColonSign 0x10020a1
#define GDK_CruzeiroSign 0x10020a2
#define GDK_FFrancSign 0x10020a3
#define GDK_LiraSign 0x10020a4
#define GDK_MillSign 0x10020a5
#define GDK_NairaSign 0x10020a6
#define GDK_PesetaSign 0x10020a7
#define GDK_RupeeSign 0x10020a8
#define GDK_WonSign 0x10020a9
#define GDK_NewSheqelSign 0x10020aa
#define GDK_DongSign 0x10020ab
#define GDK_EuroSign 0x20ac
#define GDK_zerosuperior 0x1002070
#define GDK_foursuperior 0x1002074
#define GDK_fivesuperior 0x1002075
#define GDK_sixsuperior 0x1002076
#define GDK_sevensuperior 0x1002077
#define GDK_eightsuperior 0x1002078
#define GDK_ninesuperior 0x1002079
#define GDK_zerosubscript 0x1002080
#define GDK_onesubscript 0x1002081
#define GDK_twosubscript 0x1002082
#define GDK_threesubscript 0x1002083
#define GDK_foursubscript 0x1002084
#define GDK_fivesubscript 0x1002085
#define GDK_sixsubscript 0x1002086
#define GDK_sevensubscript 0x1002087
#define GDK_eightsubscript 0x1002088
#define GDK_ninesubscript 0x1002089
#define GDK_partdifferential 0x1002202
#define GDK_emptyset 0x1002205
#define GDK_elementof 0x1002208
#define GDK_notelementof 0x1002209
#define GDK_containsas 0x100220b
#define GDK_squareroot 0x100221a
#define GDK_cuberoot 0x100221b
#define GDK_fourthroot 0x100221c
#define GDK_dintegral 0x100222c
#define GDK_tintegral 0x100222d
#define GDK_because 0x1002235
#define GDK_approxeq 0x1002248
#define GDK_notapproxeq 0x1002247
#define GDK_notidentical 0x1002262
#define GDK_stricteq 0x1002263
#define GDK_braille_dot_1 0xfff1
#define GDK_braille_dot_2 0xfff2
#define GDK_braille_dot_3 0xfff3
#define GDK_braille_dot_4 0xfff4
#define GDK_braille_dot_5 0xfff5
#define GDK_braille_dot_6 0xfff6
#define GDK_braille_dot_7 0xfff7
#define GDK_braille_dot_8 0xfff8
#define GDK_braille_dot_9 0xfff9
#define GDK_braille_dot_10 0xfffa
#define GDK_braille_blank 0x1002800
#define GDK_braille_dots_1 0x1002801
#define GDK_braille_dots_2 0x1002802
#define GDK_braille_dots_12 0x1002803
#define GDK_braille_dots_3 0x1002804
#define GDK_braille_dots_13 0x1002805
#define GDK_braille_dots_23 0x1002806
#define GDK_braille_dots_123 0x1002807
#define GDK_braille_dots_4 0x1002808
#define GDK_braille_dots_14 0x1002809
#define GDK_braille_dots_24 0x100280a
#define GDK_braille_dots_124 0x100280b
#define GDK_braille_dots_34 0x100280c
#define GDK_braille_dots_134 0x100280d
#define GDK_braille_dots_234 0x100280e
#define GDK_braille_dots_1234 0x100280f
#define GDK_braille_dots_5 0x1002810
#define GDK_braille_dots_15 0x1002811
#define GDK_braille_dots_25 0x1002812
#define GDK_braille_dots_125 0x1002813
#define GDK_braille_dots_35 0x1002814
#define GDK_braille_dots_135 0x1002815
#define GDK_braille_dots_235 0x1002816
#define GDK_braille_dots_1235 0x1002817
#define GDK_braille_dots_45 0x1002818
#define GDK_braille_dots_145 0x1002819
#define GDK_braille_dots_245 0x100281a
#define GDK_braille_dots_1245 0x100281b
#define GDK_braille_dots_345 0x100281c
#define GDK_braille_dots_1345 0x100281d
#define GDK_braille_dots_2345 0x100281e
#define GDK_braille_dots_12345 0x100281f
#define GDK_braille_dots_6 0x1002820
#define GDK_braille_dots_16 0x1002821
#define GDK_braille_dots_26 0x1002822
#define GDK_braille_dots_126 0x1002823
#define GDK_braille_dots_36 0x1002824
#define GDK_braille_dots_136 0x1002825
#define GDK_braille_dots_236 0x1002826
#define GDK_braille_dots_1236 0x1002827
#define GDK_braille_dots_46 0x1002828
#define GDK_braille_dots_146 0x1002829
#define GDK_braille_dots_246 0x100282a
#define GDK_braille_dots_1246 0x100282b
#define GDK_braille_dots_346 0x100282c
#define GDK_braille_dots_1346 0x100282d
#define GDK_braille_dots_2346 0x100282e
#define GDK_braille_dots_12346 0x100282f
#define GDK_braille_dots_56 0x1002830
#define GDK_braille_dots_156 0x1002831
#define GDK_braille_dots_256 0x1002832
#define GDK_braille_dots_1256 0x1002833
#define GDK_braille_dots_356 0x1002834
#define GDK_braille_dots_1356 0x1002835
#define GDK_braille_dots_2356 0x1002836
#define GDK_braille_dots_12356 0x1002837
#define GDK_braille_dots_456 0x1002838
#define GDK_braille_dots_1456 0x1002839
#define GDK_braille_dots_2456 0x100283a
#define GDK_braille_dots_12456 0x100283b
#define GDK_braille_dots_3456 0x100283c
#define GDK_braille_dots_13456 0x100283d
#define GDK_braille_dots_23456 0x100283e
#define GDK_braille_dots_123456 0x100283f
#define GDK_braille_dots_7 0x1002840
#define GDK_braille_dots_17 0x1002841
#define GDK_braille_dots_27 0x1002842
#define GDK_braille_dots_127 0x1002843
#define GDK_braille_dots_37 0x1002844
#define GDK_braille_dots_137 0x1002845
#define GDK_braille_dots_237 0x1002846
#define GDK_braille_dots_1237 0x1002847
#define GDK_braille_dots_47 0x1002848
#define GDK_braille_dots_147 0x1002849
#define GDK_braille_dots_247 0x100284a
#define GDK_braille_dots_1247 0x100284b
#define GDK_braille_dots_347 0x100284c
#define GDK_braille_dots_1347 0x100284d
#define GDK_braille_dots_2347 0x100284e
#define GDK_braille_dots_12347 0x100284f
#define GDK_braille_dots_57 0x1002850
#define GDK_braille_dots_157 0x1002851
#define GDK_braille_dots_257 0x1002852
#define GDK_braille_dots_1257 0x1002853
#define GDK_braille_dots_357 0x1002854
#define GDK_braille_dots_1357 0x1002855
#define GDK_braille_dots_2357 0x1002856
#define GDK_braille_dots_12357 0x1002857
#define GDK_braille_dots_457 0x1002858
#define GDK_braille_dots_1457 0x1002859
#define GDK_braille_dots_2457 0x100285a
#define GDK_braille_dots_12457 0x100285b
#define GDK_braille_dots_3457 0x100285c
#define GDK_braille_dots_13457 0x100285d
#define GDK_braille_dots_23457 0x100285e
#define GDK_braille_dots_123457 0x100285f
#define GDK_braille_dots_67 0x1002860
#define GDK_braille_dots_167 0x1002861
#define GDK_braille_dots_267 0x1002862
#define GDK_braille_dots_1267 0x1002863
#define GDK_braille_dots_367 0x1002864
#define GDK_braille_dots_1367 0x1002865
#define GDK_braille_dots_2367 0x1002866
#define GDK_braille_dots_12367 0x1002867
#define GDK_braille_dots_467 0x1002868
#define GDK_braille_dots_1467 0x1002869
#define GDK_braille_dots_2467 0x100286a
#define GDK_braille_dots_12467 0x100286b
#define GDK_braille_dots_3467 0x100286c
#define GDK_braille_dots_13467 0x100286d
#define GDK_braille_dots_23467 0x100286e
#define GDK_braille_dots_123467 0x100286f
#define GDK_braille_dots_567 0x1002870
#define GDK_braille_dots_1567 0x1002871
#define GDK_braille_dots_2567 0x1002872
#define GDK_braille_dots_12567 0x1002873
#define GDK_braille_dots_3567 0x1002874
#define GDK_braille_dots_13567 0x1002875
#define GDK_braille_dots_23567 0x1002876
#define GDK_braille_dots_123567 0x1002877
#define GDK_braille_dots_4567 0x1002878
#define GDK_braille_dots_14567 0x1002879
#define GDK_braille_dots_24567 0x100287a
#define GDK_braille_dots_124567 0x100287b
#define GDK_braille_dots_34567 0x100287c
#define GDK_braille_dots_134567 0x100287d
#define GDK_braille_dots_234567 0x100287e
#define GDK_braille_dots_1234567 0x100287f
#define GDK_braille_dots_8 0x1002880
#define GDK_braille_dots_18 0x1002881
#define GDK_braille_dots_28 0x1002882
#define GDK_braille_dots_128 0x1002883
#define GDK_braille_dots_38 0x1002884
#define GDK_braille_dots_138 0x1002885
#define GDK_braille_dots_238 0x1002886
#define GDK_braille_dots_1238 0x1002887
#define GDK_braille_dots_48 0x1002888
#define GDK_braille_dots_148 0x1002889
#define GDK_braille_dots_248 0x100288a
#define GDK_braille_dots_1248 0x100288b
#define GDK_braille_dots_348 0x100288c
#define GDK_braille_dots_1348 0x100288d
#define GDK_braille_dots_2348 0x100288e
#define GDK_braille_dots_12348 0x100288f
#define GDK_braille_dots_58 0x1002890
#define GDK_braille_dots_158 0x1002891
#define GDK_braille_dots_258 0x1002892
#define GDK_braille_dots_1258 0x1002893
#define GDK_braille_dots_358 0x1002894
#define GDK_braille_dots_1358 0x1002895
#define GDK_braille_dots_2358 0x1002896
#define GDK_braille_dots_12358 0x1002897
#define GDK_braille_dots_458 0x1002898
#define GDK_braille_dots_1458 0x1002899
#define GDK_braille_dots_2458 0x100289a
#define GDK_braille_dots_12458 0x100289b
#define GDK_braille_dots_3458 0x100289c
#define GDK_braille_dots_13458 0x100289d
#define GDK_braille_dots_23458 0x100289e
#define GDK_braille_dots_123458 0x100289f
#define GDK_braille_dots_68 0x10028a0
#define GDK_braille_dots_168 0x10028a1
#define GDK_braille_dots_268 0x10028a2
#define GDK_braille_dots_1268 0x10028a3
#define GDK_braille_dots_368 0x10028a4
#define GDK_braille_dots_1368 0x10028a5
#define GDK_braille_dots_2368 0x10028a6
#define GDK_braille_dots_12368 0x10028a7
#define GDK_braille_dots_468 0x10028a8
#define GDK_braille_dots_1468 0x10028a9
#define GDK_braille_dots_2468 0x10028aa
#define GDK_braille_dots_12468 0x10028ab
#define GDK_braille_dots_3468 0x10028ac
#define GDK_braille_dots_13468 0x10028ad
#define GDK_braille_dots_23468 0x10028ae
#define GDK_braille_dots_123468 0x10028af
#define GDK_braille_dots_568 0x10028b0
#define GDK_braille_dots_1568 0x10028b1
#define GDK_braille_dots_2568 0x10028b2
#define GDK_braille_dots_12568 0x10028b3
#define GDK_braille_dots_3568 0x10028b4
#define GDK_braille_dots_13568 0x10028b5
#define GDK_braille_dots_23568 0x10028b6
#define GDK_braille_dots_123568 0x10028b7
#define GDK_braille_dots_4568 0x10028b8
#define GDK_braille_dots_14568 0x10028b9
#define GDK_braille_dots_24568 0x10028ba
#define GDK_braille_dots_124568 0x10028bb
#define GDK_braille_dots_34568 0x10028bc
#define GDK_braille_dots_134568 0x10028bd
#define GDK_braille_dots_234568 0x10028be
#define GDK_braille_dots_1234568 0x10028bf
#define GDK_braille_dots_78 0x10028c0
#define GDK_braille_dots_178 0x10028c1
#define GDK_braille_dots_278 0x10028c2
#define GDK_braille_dots_1278 0x10028c3
#define GDK_braille_dots_378 0x10028c4
#define GDK_braille_dots_1378 0x10028c5
#define GDK_braille_dots_2378 0x10028c6
#define GDK_braille_dots_12378 0x10028c7
#define GDK_braille_dots_478 0x10028c8
#define GDK_braille_dots_1478 0x10028c9
#define GDK_braille_dots_2478 0x10028ca
#define GDK_braille_dots_12478 0x10028cb
#define GDK_braille_dots_3478 0x10028cc
#define GDK_braille_dots_13478 0x10028cd
#define GDK_braille_dots_23478 0x10028ce
#define GDK_braille_dots_123478 0x10028cf
#define GDK_braille_dots_578 0x10028d0
#define GDK_braille_dots_1578 0x10028d1
#define GDK_braille_dots_2578 0x10028d2
#define GDK_braille_dots_12578 0x10028d3
#define GDK_braille_dots_3578 0x10028d4
#define GDK_braille_dots_13578 0x10028d5
#define GDK_braille_dots_23578 0x10028d6
#define GDK_braille_dots_123578 0x10028d7
#define GDK_braille_dots_4578 0x10028d8
#define GDK_braille_dots_14578 0x10028d9
#define GDK_braille_dots_24578 0x10028da
#define GDK_braille_dots_124578 0x10028db
#define GDK_braille_dots_34578 0x10028dc
#define GDK_braille_dots_134578 0x10028dd
#define GDK_braille_dots_234578 0x10028de
#define GDK_braille_dots_1234578 0x10028df
#define GDK_braille_dots_678 0x10028e0
#define GDK_braille_dots_1678 0x10028e1
#define GDK_braille_dots_2678 0x10028e2
#define GDK_braille_dots_12678 0x10028e3
#define GDK_braille_dots_3678 0x10028e4
#define GDK_braille_dots_13678 0x10028e5
#define GDK_braille_dots_23678 0x10028e6
#define GDK_braille_dots_123678 0x10028e7
#define GDK_braille_dots_4678 0x10028e8
#define GDK_braille_dots_14678 0x10028e9
#define GDK_braille_dots_24678 0x10028ea
#define GDK_braille_dots_124678 0x10028eb
#define GDK_braille_dots_34678 0x10028ec
#define GDK_braille_dots_134678 0x10028ed
#define GDK_braille_dots_234678 0x10028ee
#define GDK_braille_dots_1234678 0x10028ef
#define GDK_braille_dots_5678 0x10028f0
#define GDK_braille_dots_15678 0x10028f1
#define GDK_braille_dots_25678 0x10028f2
#define GDK_braille_dots_125678 0x10028f3
#define GDK_braille_dots_35678 0x10028f4
#define GDK_braille_dots_135678 0x10028f5
#define GDK_braille_dots_235678 0x10028f6
#define GDK_braille_dots_1235678 0x10028f7
#define GDK_braille_dots_45678 0x10028f8
#define GDK_braille_dots_145678 0x10028f9
#define GDK_braille_dots_245678 0x10028fa
#define GDK_braille_dots_1245678 0x10028fb
#define GDK_braille_dots_345678 0x10028fc
#define GDK_braille_dots_1345678 0x10028fd
#define GDK_braille_dots_2345678 0x10028fe
#define GDK_braille_dots_12345678 0x10028ff
#define GDK_ModeLock 0x1008ff01
#define GDK_MonBrightnessUp 0x1008ff02
#define GDK_MonBrightnessDown 0x1008ff03
#define GDK_KbdLightOnOff 0x1008ff04
#define GDK_KbdBrightnessUp 0x1008ff05
#define GDK_KbdBrightnessDown 0x1008ff06
#define GDK_Standby 0x1008ff10
#define GDK_AudioLowerVolume 0x1008ff11
#define GDK_AudioMute 0x1008ff12
#define GDK_AudioRaiseVolume 0x1008ff13
#define GDK_AudioPlay 0x1008ff14
#define GDK_AudioStop 0x1008ff15
#define GDK_AudioPrev 0x1008ff16
#define GDK_AudioNext 0x1008ff17
#define GDK_HomePage 0x1008ff18
#define GDK_Mail 0x1008ff19
#define GDK_Start 0x1008ff1a
#define GDK_Search 0x1008ff1b
#define GDK_AudioRecord 0x1008ff1c
#define GDK_Calculator 0x1008ff1d
#define GDK_Memo 0x1008ff1e
#define GDK_ToDoList 0x1008ff1f
#define GDK_Calendar 0x1008ff20
#define GDK_PowerDown 0x1008ff21
#define GDK_ContrastAdjust 0x1008ff22
#define GDK_RockerUp 0x1008ff23
#define GDK_RockerDown 0x1008ff24
#define GDK_RockerEnter 0x1008ff25
#define GDK_Back 0x1008ff26
#define GDK_Forward 0x1008ff27
#define GDK_Stop 0x1008ff28
#define GDK_Refresh 0x1008ff29
#define GDK_PowerOff 0x1008ff2a
#define GDK_WakeUp 0x1008ff2b
#define GDK_Eject 0x1008ff2c
#define GDK_ScreenSaver 0x1008ff2d
#define GDK_WWW 0x1008ff2e
#define GDK_Sleep 0x1008ff2f
#define GDK_Favorites 0x1008ff30
#define GDK_AudioPause 0x1008ff31
#define GDK_AudioMedia 0x1008ff32
#define GDK_MyComputer 0x1008ff33
#define GDK_VendorHome 0x1008ff34
#define GDK_LightBulb 0x1008ff35
#define GDK_Shop 0x1008ff36
#define GDK_History 0x1008ff37
#define GDK_OpenURL 0x1008ff38
#define GDK_AddFavorite 0x1008ff39
#define GDK_HotLinks 0x1008ff3a
#define GDK_BrightnessAdjust 0x1008ff3b
#define GDK_Finance 0x1008ff3c
#define GDK_Community 0x1008ff3d
#define GDK_AudioRewind 0x1008ff3e
#define GDK_BackForward 0x1008ff3f
#define GDK_Launch0 0x1008ff40
#define GDK_Launch1 0x1008ff41
#define GDK_Launch2 0x1008ff42
#define GDK_Launch3 0x1008ff43
#define GDK_Launch4 0x1008ff44
#define GDK_Launch5 0x1008ff45
#define GDK_Launch6 0x1008ff46
#define GDK_Launch7 0x1008ff47
#define GDK_Launch8 0x1008ff48
#define GDK_Launch9 0x1008ff49
#define GDK_LaunchA 0x1008ff4a
#define GDK_LaunchB 0x1008ff4b
#define GDK_LaunchC 0x1008ff4c
#define GDK_LaunchD 0x1008ff4d
#define GDK_LaunchE 0x1008ff4e
#define GDK_LaunchF 0x1008ff4f
#define GDK_ApplicationLeft 0x1008ff50
#define GDK_ApplicationRight 0x1008ff51
#define GDK_Book 0x1008ff52
#define GDK_CD 0x1008ff53
#define GDK_WindowClear 0x1008ff55
#define GDK_Close 0x1008ff56
#define GDK_Copy 0x1008ff57
#define GDK_Cut 0x1008ff58
#define GDK_Display 0x1008ff59
#define GDK_DOS 0x1008ff5a
#define GDK_Documents 0x1008ff5b
#define GDK_Excel 0x1008ff5c
#define GDK_Explorer 0x1008ff5d
#define GDK_Game 0x1008ff5e
#define GDK_Go 0x1008ff5f
#define GDK_iTouch 0x1008ff60
#define GDK_LogOff 0x1008ff61
#define GDK_Market 0x1008ff62
#define GDK_Meeting 0x1008ff63
#define GDK_MenuKB 0x1008ff65
#define GDK_MenuPB 0x1008ff66
#define GDK_MySites 0x1008ff67
#define GDK_New 0x1008ff68
#define GDK_News 0x1008ff69
#define GDK_OfficeHome 0x1008ff6a
#define GDK_Open 0x1008ff6b
#define GDK_Option 0x1008ff6c
#define GDK_Paste 0x1008ff6d
#define GDK_Phone 0x1008ff6e
#define GDK_Reply 0x1008ff72
#define GDK_Reload 0x1008ff73
#define GDK_RotateWindows 0x1008ff74
#define GDK_RotationPB 0x1008ff75
#define GDK_RotationKB 0x1008ff76
#define GDK_Save 0x1008ff77
#define GDK_ScrollUp 0x1008ff78
#define GDK_ScrollDown 0x1008ff79
#define GDK_ScrollClick 0x1008ff7a
#define GDK_Send 0x1008ff7b
#define GDK_Spell 0x1008ff7c
#define GDK_SplitScreen 0x1008ff7d
#define GDK_Support 0x1008ff7e
#define GDK_TaskPane 0x1008ff7f
#define GDK_Terminal 0x1008ff80
#define GDK_Tools 0x1008ff81
#define GDK_Travel 0x1008ff82
#define GDK_UserPB 0x1008ff84
#define GDK_User1KB 0x1008ff85
#define GDK_User2KB 0x1008ff86
#define GDK_Video 0x1008ff87
#define GDK_WheelButton 0x1008ff88
#define GDK_Word 0x1008ff89
#define GDK_Xfer 0x1008ff8a
#define GDK_ZoomIn 0x1008ff8b
#define GDK_ZoomOut 0x1008ff8c
#define GDK_Away 0x1008ff8d
#define GDK_Messenger 0x1008ff8e
#define GDK_WebCam 0x1008ff8f
#define GDK_MailForward 0x1008ff90
#define GDK_Pictures 0x1008ff91
#define GDK_Music 0x1008ff92
#define GDK_Battery 0x1008ff93
#define GDK_Bluetooth 0x1008ff94
#define GDK_WLAN 0x1008ff95
#define GDK_UWB 0x1008ff96
#define GDK_AudioForward 0x1008ff97
#define GDK_AudioRepeat 0x1008ff98
#define GDK_AudioRandomPlay 0x1008ff99
#define GDK_Subtitle 0x1008ff9a
#define GDK_AudioCycleTrack 0x1008ff9b
#define GDK_CycleAngle 0x1008ff9c
#define GDK_FrameBack 0x1008ff9d
#define GDK_FrameForward 0x1008ff9e
#define GDK_Time 0x1008ff9f
#define GDK_SelectButton 0x1008ffa0
#define GDK_View 0x1008ffa1
#define GDK_TopMenu 0x1008ffa2
#define GDK_Red 0x1008ffa3
#define GDK_Green 0x1008ffa4
#define GDK_Yellow 0x1008ffa5
#define GDK_Blue 0x1008ffa6
#define GDK_Suspend 0x1008ffa7
#define GDK_Hibernate 0x1008ffa8
#define GDK_TouchpadToggle 0x1008ffa9
#define GDK_Switch_VT_1 0x1008fe01
#define GDK_Switch_VT_2 0x1008fe02
#define GDK_Switch_VT_3 0x1008fe03
#define GDK_Switch_VT_4 0x1008fe04
#define GDK_Switch_VT_5 0x1008fe05
#define GDK_Switch_VT_6 0x1008fe06
#define GDK_Switch_VT_7 0x1008fe07
#define GDK_Switch_VT_8 0x1008fe08
#define GDK_Switch_VT_9 0x1008fe09
#define GDK_Switch_VT_10 0x1008fe0a
#define GDK_Switch_VT_11 0x1008fe0b
#define GDK_Switch_VT_12 0x1008fe0c
#define GDK_Ungrab 0x1008fe20
#define GDK_ClearGrab 0x1008fe21
#define GDK_Next_VMode 0x1008fe22
#define GDK_Prev_VMode 0x1008fe23

#endif /* __GDK_KEYSYMS_COMPAT_H__ */
