# ClassicLadder 技术报告

## 1. 项目概述

ClassicLadder是一个开源的梯形图编程软件，用C语言开发，提供了完整的PLC编程环境。该项目始于2001年，由Marc Le Douarain开发，采用LGPL v3许可证发布。

### 1.1 主要特性

- **100% C语言实现**，具有良好的可移植性
- **支持梯形图(Ladder)和顺序控制图(Sequential/Grafcet)两种编程语言**
- **实时支持**：可与RTLinux、RTAI或Xenomai实时系统集成
- **跨平台**：支持Linux、Windows，以及嵌入式平台(Raspberry Pi、Atmel SAM等)
- **完整的GUI界面**：基于GTK 2/3开发
- **通信协议支持**：内置Modbus TCP/RTU主从站功能
- **硬件接口**：支持多种I/O接口(并口、Comedi、GPIO等)

## 2. 系统架构

## 3. 核心模块分析

### 3.1 逻辑引擎模块

#### 3.1.1 计算引擎 (calc.c)

**主要功能：**

- 梯形图逻辑运算的核心引擎
- 支持基本逻辑元素：触点、线圈、定时器、计数器等
- 实现跳转、子程序调用等控制功能

**关键接口：**

````c
void InitRungs(void);
void RefreshASection( StrSection * pSection );
void ClassicLadder_RefreshAllSections(void);
int ReadVarForElement( StrElement * pElem, char * pInfoVarIsSet );
void WriteVarForElement( StrElement *pElem, int Value );
````

#### 3.1.2 顺序控制引擎 (calc_sequential.c)

**主要功能：**

- 实现Grafcet/SFC顺序控制逻辑
- 步骤(Step)和转换(Transition)的状态管理
- 支持并行分支和汇合

**关键接口：**

````c
void InitSequential( void );
void PrepareSequential( void );
void RefreshSequentialPage( int PageNbr );
````

#### 3.1.3 变量访问模块 (vars_access.c)

**主要功能：**

- 统一的变量访问接口
- 支持多种变量类型：%I(输入)、%Q(输出)、%M(内存)、%W(字)等
- 变量过滤和状态管理

**关键接口：**

````c
void InitVars( char DoLogEvents );
int ReadVar(int TypeVar,int Offset);
void WriteVar(int TypeVar,int NumVar,int Value);
void SetVar( int TypeVar, int NumVar, int SetValue );
````

### 3.2 通信模块

#### 3.2.1 Modbus主站模块 (protocol_modbus_master.c)

**主要功能：**

- 实现Modbus TCP/RTU主站功能
- 支持多从站轮询
- 自动重连和错误处理

**关键数据结构：**

````c
typedef struct StrModbusSlave
{
    char SlaveAdr[ LGT_SLAVE_ADR ];
    int SlavePortIP;
    char UseUdpInsteadOfTcp;
    char NameInfo[ LGT_SLAVE_NAME ];
    int StatsNbrErrorsNoResponse;
    int StatsNbrErrorsModbusTreat;
    int StatsNbrFrames;
}StrModbusSlave;
````

#### 3.2.2 Modbus从站模块 (protocol_modbus_slave.c)

**主要功能：**

- 实现Modbus TCP从站功能
- 支持标准Modbus功能码(1,2,3,4,5,6,15,16)
- 与内部变量系统集成

### 3.3 硬件抽象层

#### 3.3.1 硬件接口模块 (hardware.c)

**主要功能：**

- 统一的硬件I/O接口
- 支持多种硬件平台：x86并口、Comedi、GPIO等
- 硬件配置和初始化

**关键接口：**

````c
char InitHardware( void );
void ReadPhysicalInputs( void );
void WritePhysicalOutputs( char OnlyForLifeLed );
void ConfigHardware( char ForOutputs );
````

### 3.4 文件管理模块

#### 3.4.1 项目文件管理 (files_project.c)

**主要功能：**

- 项目文件的加载和保存
- 支持压缩格式(.clprjz)
- 多文件合并和分离

**关键接口：**

````c
char LoadProjectFiles( char * FileProject );
char SaveProjectFiles( char * FileProject );
char JoinFiles( char * DirAndNameOfProject, char * TmpDirectoryFiles, char CompressedProject );
char SplitFiles( char * DirAndNameOfProject, char * TmpDirectoryFiles );
````

## 4. 数据流架构

## 5. 支持的功能元素

### 5.1 梯形图元素

- **触点类型**：常开触点、常闭触点、上升沿、下降沿
- **线圈类型**：普通线圈、取反线圈、置位线圈、复位线圈、跳转线圈、调用线圈
- **功能块**：定时器(IEC标准)、计数器、比较块、运算块、寄存器块
- **连接元素**：导线、长导线、连接点

### 5.2 顺序控制元素

- **步骤**：普通步骤、初始步骤
- **转换**：单转换、OR转换、AND转换
- **连接**：步骤间连接、并行分支、汇合
- **注释**：步骤注释、页面注释

### 5.3 变量类型

## 6. 通信协议支持

### 6.1 Modbus协议

- **Modbus TCP**：标准以太网Modbus通信
- **Modbus RTU**：串口Modbus通信
- **支持功能码**：01,02,03,04,05,06,15,16
- **主从站模式**：可同时作为主站和从站运行

### 6.2 监控协议

- **UDP监控**：基于UDP的远程监控协议
- **串口监控**：通过串口进行远程监控
- **项目传输**：支持远程项目上传下载

## 7. 实时性能

### 7.1 实时系统支持

- **Xenomai**：推荐的实时系统(用户空间实时)
- **RTAI**：内核空间实时支持(已不维护)
- **RTLinux**：早期实时系统支持(已不维护)

### 7.2 任务调度

## 8. 软PLC开发参考价值

### 8.1 可借鉴的优点

#### 8.1.1 架构设计

- **模块化设计**：清晰的分层架构，便于维护和扩展
- **硬件抽象**：统一的硬件接口，支持多平台移植
- **实时支持**：与实时系统的良好集成
- **通信协议**：完整的Modbus实现，可直接复用

#### 8.1.2 编程语言支持

- **双语言支持**：同时支持梯形图和顺序控制图
- **IEC标准**：遵循IEC 61131-3标准的定时器等功能块
- **表达式计算**：支持复杂的算术和逻辑表达式

#### 8.1.3 用户界面

- **直观的图形编辑器**：所见即所得的梯形图编辑
- **在线监控**：实时变量监控和调试功能
- **项目管理**：完整的项目文件管理系统

#### 8.1.4 扩展性

- **插件化硬件支持**：易于添加新的硬件平台
- **开放的通信接口**：支持自定义通信协议
- **符号表管理**：支持变量符号化编程

### 8.2 可改进的方面

#### 8.2.1 现代化改进

- **界面框架升级**：从GTK迁移到更现代的UI框架(Qt、Web等)
- **编程语言扩展**：增加结构化文本(ST)、功能块图(FBD)等IEC语言
- **数据类型丰富**：增加浮点数、字符串、结构体等数据类型

#### 8.2.2 性能优化

- **多线程优化**：更好的并行处理能力
- **内存管理**：动态内存分配和垃圾回收
- **编译优化**：将解释执行改为编译执行

#### 8.2.3 功能增强

- **网络安全**：增加加密和认证机制
- **云端集成**：支持云端编程和远程维护
- **AI集成**：集成机器学习和预测性维护功能

#### 8.2.4 开发工具

- **版本控制**：集成Git等版本控制系统
- **单元测试**：内置测试框架和仿真环境
- **性能分析**：代码性能分析和优化工具

## 9. 软PLC开发建议

### 9.1 架构建议
