# Details

Date : 2025-05-08 15:46:11

Directory c:\\home\\project\\SoftPLC\\classicladder-main

Total : 135 files,  36019 codes, 4905 comments, 3077 blanks, all 44001 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [old\_files/gdk\_includes\_update/gdkkeysyms-compat.h](/old_files/gdk_includes_update/gdkkeysyms-compat.h) | C++ | 2,178 | 26 | 5 | 2,209 |
| [old\_files/gdk\_includes\_update/gdkkeysyms.h](/old_files/gdk_includes_update/gdkkeysyms.h) | C++ | 2,181 | 33 | 8 | 2,222 |
| [src/Makefile](/src/Makefile) | Makefile | 381 | 70 | 106 | 557 |
| [src/arithm\_eval.c](/src/arithm_eval.c) | C | 728 | 112 | 47 | 887 |
| [src/arithm\_eval.h](/src/arithm_eval.h) | C++ | 8 | 0 | 6 | 14 |
| [src/arrays.c](/src/arrays.c) | C | 529 | 67 | 60 | 656 |
| [src/base64.c](/src/base64.c) | C | 72 | 52 | 28 | 152 |
| [src/base64.h](/src/base64.h) | C++ | 2 | 5 | 3 | 10 |
| [src/cJSON.c](/src/cJSON.c) | C | 480 | 70 | 70 | 620 |
| [src/cJSON.h](/src/cJSON.h) | C++ | 80 | 49 | 28 | 157 |
| [src/calc.c](/src/calc.c) | C | 937 | 85 | 39 | 1,061 |
| [src/calc.h](/src/calc.h) | C++ | 17 | 4 | 1 | 22 |
| [src/calc\_sequential.c](/src/calc_sequential.c) | C | 140 | 30 | 16 | 186 |
| [src/calc\_sequential.h](/src/calc_sequential.h) | C++ | 3 | 0 | 1 | 4 |
| [src/classicladder.c](/src/classicladder.c) | C | 733 | 101 | 72 | 906 |
| [src/classicladder.h](/src/classicladder.h) | C++ | 670 | 94 | 102 | 866 |
| [src/classicladder\_gtk.c](/src/classicladder_gtk.c) | C | 1,511 | 268 | 140 | 1,919 |
| [src/classicladder\_gtk.h](/src/classicladder_gtk.h) | C++ | 72 | 2 | 6 | 80 |
| [src/classiclauncher.c](/src/classiclauncher.c) | C | 122 | 13 | 15 | 150 |
| [src/config.c](/src/config.c) | C | 84 | 26 | 15 | 125 |
| [src/config.h](/src/config.h) | C++ | 1 | 0 | 1 | 2 |
| [src/config\_gtk.c](/src/config_gtk.c) | C | 1,468 | 216 | 84 | 1,768 |
| [src/config\_gtk.h](/src/config_gtk.h) | C++ | 2 | 0 | 1 | 3 |
| [src/drawing.c](/src/drawing.c) | C | 1,027 | 193 | 58 | 1,278 |
| [src/drawing.h](/src/drawing.h) | C++ | 18 | 0 | 3 | 21 |
| [src/drawing\_sequential.c](/src/drawing_sequential.c) | C | 507 | 82 | 32 | 621 |
| [src/drawing\_sequential.h](/src/drawing_sequential.h) | C++ | 4 | 0 | 3 | 7 |
| [src/edit.c](/src/edit.c) | C | 1,340 | 161 | 71 | 1,572 |
| [src/edit.h](/src/edit.h) | C++ | 28 | 0 | 3 | 31 |
| [src/edit\_copy.c](/src/edit_copy.c) | C | 158 | 27 | 15 | 200 |
| [src/edit\_copy.h](/src/edit_copy.h) | C++ | 7 | 0 | 3 | 10 |
| [src/edit\_gtk.c](/src/edit_gtk.c) | C | 694 | 76 | 48 | 818 |
| [src/edit\_gtk.h](/src/edit_gtk.h) | C++ | 51 | 0 | 3 | 54 |
| [src/edit\_sequential.c](/src/edit_sequential.c) | C | 1,018 | 72 | 52 | 1,142 |
| [src/edit\_sequential.h](/src/edit_sequential.h) | C++ | 7 | 0 | 1 | 8 |
| [src/editproperties\_gtk.c](/src/editproperties_gtk.c) | C | 240 | 108 | 34 | 382 |
| [src/editproperties\_gtk.h](/src/editproperties_gtk.h) | C++ | 4 | 0 | 1 | 5 |
| [src/files.c](/src/files.c) | C | 2,030 | 158 | 93 | 2,281 |
| [src/files.h](/src/files.h) | C++ | 49 | 3 | 10 | 62 |
| [src/files\_project.c](/src/files_project.c) | C | 401 | 111 | 41 | 553 |
| [src/files\_project.h](/src/files_project.h) | C++ | 12 | 0 | 4 | 16 |
| [src/files\_sequential.c](/src/files_sequential.c) | C | 217 | 26 | 14 | 257 |
| [src/files\_sequential.h](/src/files_sequential.h) | C++ | 2 | 0 | 2 | 4 |
| [src/frames\_log\_buffers.c](/src/frames_log_buffers.c) | C | 192 | 30 | 21 | 243 |
| [src/frames\_log\_buffers.h](/src/frames_log_buffers.h) | C++ | 26 | 4 | 10 | 40 |
| [src/global.h](/src/global.h) | C++ | 69 | 20 | 25 | 114 |
| [src/hardware.c](/src/hardware.c) | C | 589 | 62 | 41 | 692 |
| [src/hardware.h](/src/hardware.h) | C++ | 9 | 4 | 7 | 20 |
| [src/icons\_gtk.c](/src/icons_gtk.c) | C | 134 | 32 | 18 | 184 |
| [src/icons\_gtk.h](/src/icons_gtk.h) | C++ | 3 | 0 | 2 | 5 |
| [src/ladderlib.c](/src/ladderlib.c) | C | 112 | 26 | 16 | 154 |
| [src/log\_events.c](/src/log_events.c) | C | 673 | 97 | 38 | 808 |
| [src/log\_events.h](/src/log_events.h) | C++ | 22 | 0 | 7 | 29 |
| [src/log\_events\_gtk.c](/src/log_events_gtk.c) | C | 296 | 51 | 42 | 389 |
| [src/log\_events\_gtk.h](/src/log_events_gtk.h) | C++ | 6 | 0 | 2 | 8 |
| [src/manager.c](/src/manager.c) | C | 283 | 41 | 24 | 348 |
| [src/manager.h](/src/manager.h) | C++ | 13 | 0 | 2 | 15 |
| [src/manager\_gtk.c](/src/manager_gtk.c) | C | 545 | 96 | 56 | 697 |
| [src/manager\_gtk.h](/src/manager_gtk.h) | C++ | 13 | 1 | 1 | 15 |
| [src/manager\_gtk\_OLD\_CLIST.c](/src/manager_gtk_OLD_CLIST.c) | C | 382 | 75 | 41 | 498 |
| [src/matplc.conf](/src/matplc.conf) | Properties | 7 | 8 | 8 | 23 |
| [src/menu\_and\_toolbar\_gtk.c](/src/menu_and_toolbar_gtk.c) | C | 535 | 42 | 40 | 617 |
| [src/menu\_and\_toolbar\_gtk.h](/src/menu_and_toolbar_gtk.h) | C++ | 20 | 0 | 2 | 22 |
| [src/modem.c](/src/modem.c) | C | 287 | 46 | 26 | 359 |
| [src/modem.h](/src/modem.h) | C++ | 10 | 0 | 3 | 13 |
| [src/module\_rtai.c](/src/module_rtai.c) | C | 95 | 29 | 16 | 140 |
| [src/module\_rtlinux.c](/src/module_rtlinux.c) | C | 71 | 19 | 19 | 109 |
| [src/module\_rtlinux.h](/src/module_rtlinux.h) | C++ | 1 | 0 | 2 | 3 |
| [src/monitor\_protocol.c](/src/monitor_protocol.c) | C | 1,171 | 232 | 118 | 1,521 |
| [src/monitor\_protocol.h](/src/monitor_protocol.h) | C++ | 36 | 0 | 11 | 47 |
| [src/monitor\_protocol\_adds\_serial.c](/src/monitor_protocol_adds_serial.c) | C | 77 | 55 | 11 | 143 |
| [src/monitor\_protocol\_adds\_serial.h](/src/monitor_protocol_adds_serial.h) | C++ | 17 | 0 | 10 | 27 |
| [src/monitor\_serial\_config\_window\_gtk.c](/src/monitor_serial_config_window_gtk.c) | C | 87 | 23 | 15 | 125 |
| [src/monitor\_serial\_config\_window\_gtk.h](/src/monitor_serial_config_window_gtk.h) | C++ | 2 | 0 | 2 | 4 |
| [src/monitor\_sockets\_udp.c](/src/monitor_sockets_udp.c) | C | 115 | 33 | 17 | 165 |
| [src/monitor\_sockets\_udp.h](/src/monitor_sockets_udp.h) | C++ | 41 | 0 | 14 | 55 |
| [src/monitor\_threads.c](/src/monitor_threads.c) | C | 606 | 69 | 39 | 714 |
| [src/monitor\_threads.h](/src/monitor_threads.h) | C++ | 7 | 1 | 3 | 11 |
| [src/monitor\_transfer.c](/src/monitor_transfer.c) | C | 635 | 80 | 28 | 743 |
| [src/monitor\_transfer.h](/src/monitor_transfer.h) | C++ | 27 | 0 | 10 | 37 |
| [src/monitor\_windows\_gtk.c](/src/monitor_windows_gtk.c) | C | 530 | 102 | 58 | 690 |
| [src/monitor\_windows\_gtk.h](/src/monitor_windows_gtk.h) | C++ | 23 | 2 | 7 | 32 |
| [src/network\_config\_window\_gtk.c](/src/network_config_window_gtk.c) | C | 95 | 23 | 16 | 134 |
| [src/network\_config\_window\_gtk.h](/src/network_config_window_gtk.h) | C++ | 2 | 0 | 3 | 5 |
| [src/preferences.c](/src/preferences.c) | C | 468 | 33 | 19 | 520 |
| [src/preferences.h](/src/preferences.h) | C++ | 16 | 0 | 5 | 21 |
| [src/print\_gnome.c](/src/print_gnome.c) | C | 174 | 27 | 40 | 241 |
| [src/print\_gnome.h](/src/print_gnome.h) | C++ | 2 | 0 | 2 | 4 |
| [src/print\_gtk.c](/src/print_gtk.c) | C | 273 | 28 | 39 | 340 |
| [src/print\_gtk.h](/src/print_gtk.h) | C++ | 2 | 0 | 2 | 4 |
| [src/protocol\_modbus\_defines.h](/src/protocol_modbus_defines.h) | C++ | 24 | 3 | 8 | 35 |
| [src/protocol\_modbus\_master.c](/src/protocol_modbus_master.c) | C | 762 | 74 | 32 | 868 |
| [src/protocol\_modbus\_master.h](/src/protocol_modbus_master.h) | C++ | 70 | 9 | 12 | 91 |
| [src/protocol\_modbus\_slave.c](/src/protocol_modbus_slave.c) | C | 255 | 53 | 21 | 329 |
| [src/protocol\_modbus\_slave.h](/src/protocol_modbus_slave.h) | C++ | 4 | 0 | 3 | 7 |
| [src/search.c](/src/search.c) | C | 378 | 40 | 35 | 453 |
| [src/search.h](/src/search.h) | C++ | 9 | 0 | 3 | 12 |
| [src/sequential.h](/src/sequential.h) | C++ | 58 | 44 | 22 | 124 |
| [src/serial\_common.h](/src/serial_common.h) | C++ | 17 | 2 | 4 | 23 |
| [src/serial\_linux.c](/src/serial_linux.c) | C | 249 | 44 | 24 | 317 |
| [src/serial\_test\_rs485.c](/src/serial_test_rs485.c) | C | 59 | 4 | 10 | 73 |
| [src/serial\_win.c](/src/serial_win.c) | C | 171 | 40 | 21 | 232 |
| [src/socket\_modbus\_master.c](/src/socket_modbus_master.c) | C | 369 | 57 | 40 | 466 |
| [src/socket\_modbus\_master.h](/src/socket_modbus_master.h) | C++ | 6 | 0 | 2 | 8 |
| [src/socket\_server.c](/src/socket_server.c) | C | 215 | 38 | 28 | 281 |
| [src/socket\_server.h](/src/socket_server.h) | C++ | 3 | 0 | 4 | 7 |
| [src/spy\_vars\_gtk.c](/src/spy_vars_gtk.c) | C | 883 | 173 | 95 | 1,151 |
| [src/spy\_vars\_gtk.h](/src/spy_vars_gtk.h) | C++ | 14 | 1 | 2 | 17 |
| [src/symbols.c](/src/symbols.c) | C | 142 | 48 | 14 | 204 |
| [src/symbols.h](/src/symbols.h) | C++ | 4 | 0 | 2 | 6 |
| [src/symbols\_gtk.c](/src/symbols_gtk.c) | C | 194 | 47 | 30 | 271 |
| [src/symbols\_gtk.h](/src/symbols_gtk.h) | C++ | 5 | 0 | 2 | 7 |
| [src/target\_embedded\_plc\_486.h](/src/target_embedded_plc_486.h) | C++ | 11 | 10 | 7 | 28 |
| [src/target\_embedded\_plc\_arietta.h](/src/target_embedded_plc_arietta.h) | C++ | 8 | 7 | 7 | 22 |
| [src/target\_embedded\_raspberrypi.h](/src/target_embedded_raspberrypi.h) | C++ | 4 | 9 | 7 | 20 |
| [src/tasks.c](/src/tasks.c) | C | 499 | 45 | 39 | 583 |
| [src/tasks.h](/src/tasks.h) | C++ | 25 | 1 | 12 | 38 |
| [src/tasks\_OLD\_beforeXeno3.c](/src/tasks_OLD_beforeXeno3.c) | C | 403 | 36 | 31 | 470 |
| [src/time\_and\_rtc.c](/src/time_and_rtc.c) | C | 208 | 39 | 17 | 264 |
| [src/time\_and\_rtc.h](/src/time_and_rtc.h) | C++ | 14 | 1 | 6 | 21 |
| [src/time\_test\_rtc.c](/src/time_test_rtc.c) | C | 44 | 4 | 8 | 56 |
| [src/vars\_access.c](/src/vars_access.c) | C | 381 | 37 | 17 | 435 |
| [src/vars\_access.h](/src/vars_access.h) | C++ | 12 | 1 | 3 | 16 |
| [src/vars\_browser\_gtk.c](/src/vars_browser_gtk.c) | C | 246 | 45 | 33 | 324 |
| [src/vars\_browser\_gtk.h](/src/vars_browser_gtk.h) | C++ | 4 | 0 | 3 | 7 |
| [src/vars\_mat.c](/src/vars_mat.c) | C | 70 | 29 | 14 | 113 |
| [src/vars\_names.c](/src/vars_names.c) | C | 680 | 65 | 41 | 786 |
| [src/vars\_names.h](/src/vars_names.h) | C++ | 4 | 0 | 4 | 8 |
| [src/vars\_names\_list.c](/src/vars_names_list.c) | C | 77 | 25 | 28 | 130 |
| [src/vars\_system.c](/src/vars_system.c) | C | 138 | 34 | 18 | 190 |
| [src/vars\_system.h](/src/vars_system.h) | C++ | 3 | 0 | 4 | 7 |
| [wiringSam/Makefile](/wiringSam/Makefile) | Makefile | 14 | 3 | 9 | 26 |
| [wiringSam/test\_at91sam\_gpio.c](/wiringSam/test_at91sam_gpio.c) | C | 28 | 17 | 8 | 53 |
| [wiringSam/wiringSam.c](/wiringSam/wiringSam.c) | C | 163 | 64 | 35 | 262 |
| [wiringSam/wiringSam.h](/wiringSam/wiringSam.h) | C++ | 80 | 25 | 29 | 134 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)