_FILES_CLASSICLADDER
_FILE-project_infos.txt
PROJECT_NAME=RaspberryPi
PROJECT_SITE=SimpleTest
PARAM_VERSION=6
PARAM_AUTHOR=MLD
PARAM_COMPANY=
CREA_DATE=14/09/15 14:44:52
MODIF_DATE=14/09/15 15:24:23
_/FILE-project_infos.txt
_FILE-general.txt
PERIODIC_REFRESH=50
PERIODIC_INPUTS_REFRESH=10
REAL_INPUTS_OUTPUTS_ONLY_ON_TARGET=0
AUTO_ADJUST_SUMMER_WINTER_TIME=0
SIZE_NBR_RUNGS=300
SIZE_NBR_BITS=500
SIZE_NBR_WORDS=200
SIZE_NBR_TIMERS=50
SIZE_NBR_MONOSTABLES=50
SIZE_NBR_COUNTERS=50
SIZE_NBR_TIMERS_IEC=50
SIZE_NBR_PHYS_INPUTS=50
SIZE_NBR_PHYS_OUTPUTS=50
SIZE_NBR_ARITHM_EXPR=200
SIZE_NBR_SECTIONS=10
SIZE_NBR_SYMBOLS=300
SIZE_NBR_PHYS_WORDS_INPUTS=25
SIZE_NBR_PHYS_WORDS_OUTPUTS=25
MODBUS_MASTER_SERIAL_PORT=
MODBUS_MASTER_SERIAL_SPEED=9600
MODBUS_MASTER_SERIAL_DATABITS=8
MODBUS_MASTER_SERIAL_PARITY=0
MODBUS_MASTER_SERIAL_STOPBITS=1
_/FILE-general.txt
_FILE-timers.csv
#VER=2.0
_/FILE-timers.csv
_FILE-monostables.csv
#VER=2.0
_/FILE-monostables.csv
_FILE-counters.csv
#VER=2.0
_/FILE-counters.csv
_FILE-timers_iec.csv
#VER=2.0
_/FILE-timers_iec.csv
_FILE-arithmetic_expressions.csv
#VER=2.0
_/FILE-arithmetic_expressions.csv
_FILE-rung_0.csv
#VER=3.0
#LABEL=
#COMMENT_LONG=GPIO11in to GPIO25out and GPIO9in to GPIO24
#PREVRUNG=-1
#NEXTRUNG=-1
#NBRLINES=8
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/1
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/2 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/2
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/1 , 1-0-50/2 , 9-0-0/0 , 1-0-70/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-65/0
0-0-50/2 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_0.csv
_FILE-sections.csv
#VER=1.0
#NAME000=Prog1
000,0,-1,0,0,0
_/FILE-sections.csv
_FILE-sequential.csv
#VER=1.0
_/FILE-sequential.csv
_FILE-symbols.csv
#VER=1.0
_/FILE-symbols.csv
_FILE-ioconf.csv
#VER=1.0
0,1,2,0,11,1,0,0
0,2,2,0,9,1,0,0
1,1,2,0,25,1,0,0
1,2,2,0,24,1,0,0
_/FILE-ioconf.csv
_FILE-com_params.txt
MODBUS_ELEMENT_OFFSET=1
MODBUS_MASTER_SERIAL_USE_RTS_TO_SEND=0
MODBUS_MASTER_TIME_INTER_FRAME=100
MODBUS_MASTER_TIME_OUT_RECEIPT=500
MODBUS_MASTER_TIME_AFTER_TRANSMIT=0
MODBUS_DEBUG_LEVEL=3
MODBUS_MAP_TYPE_FOR_READ_INPUTS=50
MODBUS_MAP_TYPE_FOR_READ_COILS=60
MODBUS_MAP_TYPE_FOR_WRITE_COILS=60
MODBUS_MAP_TYPE_FOR_READ_INPUT_REGS=270
MODBUS_MAP_TYPE_FOR_READ_HOLD_REGS=280
MODBUS_MAP_TYPE_FOR_WRITE_HOLD_REGS=280
_/FILE-com_params.txt
_FILE-modbusioconf.csv
#VER=2.0
_/FILE-modbusioconf.csv
_FILE-config_events.csv
#VER=1.0
_/FILE-config_events.csv
_FILE-modem_config.txt
MODEM_USED=0
INIT_SEQ=ATZ
CONFIG_SEQ=ATE0;ATS0=2
CALL_SEQ=ATDT
PIN_CODE=
_/FILE-modem_config.txt
_FILE-remote_alarms.txt
GLOBAL_ENABLED=0
SLOT_NAME_00=
ALARM_TYPE_00=-1
TELEPHONE_NUMBER_00=
EMAIL_ADDRESS_00=
SLOT_NAME_01=
ALARM_TYPE_01=-1
TELEPHONE_NUMBER_01=
EMAIL_ADDRESS_01=
SLOT_NAME_02=
ALARM_TYPE_02=-1
TELEPHONE_NUMBER_02=
EMAIL_ADDRESS_02=
SLOT_NAME_03=
ALARM_TYPE_03=-1
TELEPHONE_NUMBER_03=
EMAIL_ADDRESS_03=
SLOT_NAME_04=
ALARM_TYPE_04=-1
TELEPHONE_NUMBER_04=
EMAIL_ADDRESS_04=
SLOT_NAME_05=
ALARM_TYPE_05=-1
TELEPHONE_NUMBER_05=
EMAIL_ADDRESS_05=
SLOT_NAME_06=
ALARM_TYPE_06=-1
TELEPHONE_NUMBER_06=
EMAIL_ADDRESS_06=
SLOT_NAME_07=
ALARM_TYPE_07=-1
TELEPHONE_NUMBER_07=
EMAIL_ADDRESS_07=
CENTER_SERVER_SMS=
SMTP_SERVER_FOR_EMAILS=
SMTP_SERVER_USERNAME=
SMTP_SERVER_PASSWORD=
EMAIL_SENDER_ADDRESS=
_/FILE-remote_alarms.txt
_FILE-spy_vars.csv
200,200,200,200,200,200,200,270,270,200
0,1,2,3,4,5,6,0,1,1
0,0,0
0,0,0,2,3,0,3,0,0,0
_/FILE-spy_vars.csv
_/FILES_CLASSICLADDER
