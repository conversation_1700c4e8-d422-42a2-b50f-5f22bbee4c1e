_FILES_CLASSICLADDER
_FILE-project_infos.txt
PROJECT_NAME=ClassicLadder_PLC_x86
PROJECT_SITE=
PARAM_VERSION=11
PARAM_AUTHOR=MLD
PARAM_COMPANY=
CREA_DATE=12/02/20 20:34:38
MODIF_DATE=14/01/13 10:41:32
_/FILE-project_infos.txt
_FILE-general.txt
PERIODIC_REFRESH=50
PERIODIC_INPUTS_REFRESH=10
REAL_INPUTS_OUTPUTS_ONLY_ON_TARGET=1
AUTO_ADJUST_SUMMER_WINTER_TIME=0
SIZE_NBR_RUNGS=100
SIZE_NBR_BITS=500
SIZE_NBR_WORDS=100
SIZE_NBR_TIMERS=10
SIZE_NBR_MONOSTABLES=10
SIZE_NBR_COUNTERS=10
SIZE_NBR_TIMERS_IEC=10
SIZE_NBR_PHYS_INPUTS=50
SIZE_NBR_PHYS_OUTPUTS=50
SIZE_NBR_ARITHM_EXPR=100
SIZE_NBR_SECTIONS=10
SIZE_NBR_SYMBOLS=100
SIZE_NBR_PHYS_WORDS_INPUTS=25
SIZE_NBR_PHYS_WORDS_OUTPUTS=25
MODBUS_MASTER_SERIAL_PORT=
MODBUS_MASTER_SERIAL_SPEED=9600
MODBUS_MASTER_SERIAL_DATABITS=8
MODBUS_MASTER_SERIAL_PARITY=0
MODBUS_MASTER_SERIAL_STOPBITS=1
_/FILE-general.txt
_FILE-timers.csv
#VER=2.0
T0,2,5
T1,1,5
T2,2,5
T3,1,10
T4,1,10
T5,1,10
T6,1,10
T7,1,10
T8,1,10
T9,1,10
_/FILE-timers.csv
_FILE-monostables.csv
#VER=2.0
M0,1,3
M1,1,10
M2,1,10
M3,1,10
M4,1,10
M5,1,10
M6,1,10
M7,1,10
M8,1,10
M9,1,10
_/FILE-monostables.csv
_FILE-counters.csv
#VER=2.0
_/FILE-counters.csv
_FILE-timers_iec.csv
#VER=2.0
TM0,2,5,0
_/FILE-timers_iec.csv
_FILE-arithmetic_expressions.csv
#VER=2.0
0000,@200/0@=1
0001,@200/0@=2
0002,@200/0@=3
0003,@200/0@:=@200/0@+1
0007,@200/0@=4
0008,@200/0@=5
0009,@200/0@=6
0010,@200/0@=7
0011,@200/0@=8
0012,@260/0@:=1
0013,@260/0@:=5
_/FILE-arithmetic_expressions.csv
_FILE-rung_0.csv
#VER=3.0
#LABEL=
#COMMENT=copy inputs
#PREVRUNG=0
#NEXTRUNG=5
#NBRLINES=8
1-0-50/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/1
1-0-50/2 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/2
1-0-50/3 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/3
1-0-50/4 , 9-0-50/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/10 , 9-0-0/0 , 50-0-0/4
1-0-50/5 , 9-0-50/6 , 9-0-50/7 , 9-0-50/8 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/17 , 9-0-0/0 , 50-0-0/5
1-0-50/9 , 0-1-50/10 , 0-0-50/11 , 0-0-50/12 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/16 , 0-0-0/0 , 0-0-0/0
1-0-50/6 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/6
1-0-50/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_0.csv
_FILE-rung_1.csv
#VER=3.0
#LABEL=
#COMMENT=S7 or B20 for queue effect
#PREVRUNG=5
#NEXTRUNG=3
#NBRLINES=8
1-0-50/4 , 1-0-0/20 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/10
2-0-0/20 , 2-0-50/7 , 2-1-15/0 , 9-0-0/0 , 99-0-0/0 , 13-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 99-0-0/0 , 99-0-0/0 , 60-0-0/3
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 99-0-0/0 , 99-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/16 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/17 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_1.csv
_FILE-rung_3.csv
#VER=3.0
#LABEL=
#COMMENT=set outputs
#PREVRUNG=1
#NEXTRUNG=4
#NBRLINES=8
1-0-0/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/1
99-0-0/0 , 99-0-0/0 , 20-0-0/0 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-60/0 , 0-0-0/0 , 0-0-0/0
1-0-0/2 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/2
99-0-0/0 , 99-0-0/0 , 20-0-0/1 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-60/0 , 0-0-0/0 , 0-0-0/0
1-0-0/3 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/3
99-0-0/0 , 99-0-0/0 , 20-0-0/2 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-0/4 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/4
99-0-0/0 , 99-0-0/0 , 20-0-0/7 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_3.csv
_FILE-rung_4.csv
#VER=3.0
#LABEL=
#COMMENT=
#PREVRUNG=3
#NEXTRUNG=6
#NBRLINES=8
1-0-0/5 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/5
99-0-0/0 , 99-0-0/0 , 20-0-0/8 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-0/6 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/6
99-0-0/0 , 99-0-0/0 , 20-0-0/9 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-0/7 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/7
99-0-0/0 , 99-0-0/0 , 20-0-0/10 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-0/8 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/8
99-0-0/0 , 99-0-0/0 , 20-0-0/11 , 1-0-0/10 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_4.csv
_FILE-rung_5.csv
#VER=3.0
#LABEL=
#COMMENT=PLC Push Button to go faster
#PREVRUNG=0
#NEXTRUNG=1
#NBRLINES=8
1-0-50/7 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/7
1-0-50/11 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/8 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/8
1-1-50/12 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/13 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/9
0-0-0/0 , 9-1-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 99-0-0/0 , 99-0-0/0 , 60-0-0/12
2-0-50/13 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 99-0-0/0 , 99-0-0/0 , 60-0-0/13
_/FILE-rung_5.csv
_FILE-rung_6.csv
#VER=3.0
#LABEL=
#COMMENT=Modem status on QLED0
#PREVRUNG=4
#NEXTRUNG=-1
#NBRLINES=8
1-0-70/40 , 1-0-70/41 , 1-0-70/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-65/0
0-0-0/0 , 2-1-70/41 , 9-0-0/0 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_6.csv
_FILE-sections.csv
#VER=1.0
#NAME000=Prog1
000,0,-1,0,6,0
_/FILE-sections.csv
_FILE-sequential.csv
#VER=1.0
_/FILE-sequential.csv
_FILE-symbols.csv
#VER=1.0
_/FILE-symbols.csv
_FILE-ioconf.csv
#VER=1.0
0,1,0,120,0,8,1,0
0,9,0,123,0,5,1,0
1,1,0,122,0,8,0,0
1,-1,1,154,0,1,0,255
_/FILE-ioconf.csv
_FILE-com_params.txt
MODBUS_ELEMENT_OFFSET=1
MODBUS_MASTER_SERIAL_USE_RTS_TO_SEND=0
MODBUS_MASTER_TIME_INTER_FRAME=100
MODBUS_MASTER_TIME_OUT_RECEIPT=500
MODBUS_MASTER_TIME_AFTER_TRANSMIT=0
MODBUS_DEBUG_LEVEL=3
MODBUS_MAP_TYPE_FOR_READ_INPUTS=50
MODBUS_MAP_TYPE_FOR_READ_COILS=60
MODBUS_MAP_TYPE_FOR_WRITE_COILS=60
MODBUS_MAP_TYPE_FOR_READ_INPUT_REGS=270
MODBUS_MAP_TYPE_FOR_READ_HOLD_REGS=280
MODBUS_MAP_TYPE_FOR_WRITE_HOLD_REGS=280
_/FILE-com_params.txt
_FILE-modbusioconf.csv
#VER=2.0
_/FILE-modbusioconf.csv
_FILE-config_events.csv
#VER=1.0
_/FILE-config_events.csv
_FILE-modem_config.txt
MODEM_USED=1
INIT_SEQ=ATZ
CONFIG_SEQ=ATE0;ATS0=2
CALL_SEQ=ATDT
PIN_CODE=
_/FILE-modem_config.txt
_FILE-remote_alarms.txt
GLOBAL_ENABLED=0
SLOT_NAME_00=
ALARM_TYPE_00=-1
TELEPHONE_NUMBER_00=
EMAIL_ADDRESS_00=
SLOT_NAME_01=
ALARM_TYPE_01=-1
TELEPHONE_NUMBER_01=
EMAIL_ADDRESS_01=
SLOT_NAME_02=
ALARM_TYPE_02=-1
TELEPHONE_NUMBER_02=
EMAIL_ADDRESS_02=
SLOT_NAME_03=
ALARM_TYPE_03=-1
TELEPHONE_NUMBER_03=
EMAIL_ADDRESS_03=
SLOT_NAME_04=
ALARM_TYPE_04=-1
TELEPHONE_NUMBER_04=
EMAIL_ADDRESS_04=
SLOT_NAME_05=
ALARM_TYPE_05=-1
TELEPHONE_NUMBER_05=
EMAIL_ADDRESS_05=
SLOT_NAME_06=
ALARM_TYPE_06=-1
TELEPHONE_NUMBER_06=
EMAIL_ADDRESS_06=
SLOT_NAME_07=
ALARM_TYPE_07=-1
TELEPHONE_NUMBER_07=
EMAIL_ADDRESS_07=
CENTER_SERVER_SMS=
SMTP_SERVER_FOR_EMAILS=
SMTP_SERVER_USERNAME=
SMTP_SERVER_PASSWORD=
EMAIL_SENDER_ADDRESS=
_/FILE-remote_alarms.txt
_FILE-spy_vars.csv
60,60,65,200,200,70,70,270,270,200
40,41,0,3,4,40,41,0,1,1
0,0,0
3,3,3,2,3,3,3,0,0,0
_/FILE-spy_vars.csv
_/FILES_CLASSICLADDER
