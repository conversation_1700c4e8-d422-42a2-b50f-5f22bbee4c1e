_FILES_CLASSICLADDER
_FILE-project_infos.txt
PROJECT_NAME=
PROJECT_SITE=
PARAM_VERSION=1
PARAM_AUTHOR=
PARAM_COMPANY=
CREA_DATE=16/05/30 19:52:07
MODIF_DATE=16/05/30 19:52:47
PARAM_COMMENT=
_/FILE-project_infos.txt
_FILE-general.txt
PERIODIC_REFRESH=50
PERIODIC_INPUTS_REFRESH=10
REAL_INPUTS_OUTPUTS_ONLY_ON_TARGET=0
AUTO_ADJUST_SUMMER_WINTER_TIME=0
SIZE_NBR_RUNGS=300
SIZE_NBR_BITS=500
SIZE_NBR_WORDS=200
SIZE_NBR_TIMERS=50
SIZE_NBR_MONOSTABLES=50
SIZE_NBR_COUNTERS=50
SIZE_NBR_TIMERS_IEC=50
SIZE_NBR_REGISTERS=10
SIZE_REGISTER_LIST=500
SIZE_NBR_PHYS_INPUTS=50
SIZE_NBR_PHYS_OUTPUTS=50
SIZE_NBR_ARITHM_EXPR=200
SIZE_NBR_SECTIONS=10
SIZE_NBR_SYMBOLS=300
SIZE_NBR_PHYS_WORDS_INPUTS=25
SIZE_NBR_PHYS_WORDS_OUTPUTS=25
MODBUS_MASTER_SERIAL_PORT=
MODBUS_MASTER_SERIAL_SPEED=9600
MODBUS_MASTER_SERIAL_DATABITS=8
MODBUS_MASTER_SERIAL_PARITY=0
MODBUS_MASTER_SERIAL_STOPBITS=1
_/FILE-general.txt
_FILE-timers.csv
#VER=2.0
T0,0,2
T1,1,5
T2,2,5
T3,1,10
T4,1,10
T5,1,10
T6,1,10
T7,1,10
T8,1,10
T9,1,10
_/FILE-timers.csv
_FILE-monostables.csv
#VER=2.0
M0,1,3
M1,1,10
M2,1,10
M3,1,10
M4,1,10
M5,1,10
M6,1,10
M7,1,10
M8,1,10
M9,1,10
_/FILE-monostables.csv
_FILE-counters.csv
#VER=2.0
_/FILE-counters.csv
_FILE-timers_iec.csv
#VER=2.0
_/FILE-timers_iec.csv
_FILE-registers.csv
#VER=1.0
_/FILE-registers.csv
_FILE-arithmetic_expressions.csv
#VER=2.0
0000,@220/9@>5
0001,@220/4@>4
0002,@220/6@>8
_/FILE-arithmetic_expressions.csv
_FILE-rung_0.csv
#VER=3.0
#LABEL=TIMES
#COMMENT=To advance alone
#PREVRUNG=0
#NEXTRUNG=1
#NBRLINES=8
99-0-0/1 , 99-0-0/2 , 20-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/10
99-0-0/5 , 99-0-0/0 , 20-0-0/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/5
99-0-0/12 , 99-0-0/13 , 20-0-0/2 , 9-0-0/15 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/7
0-0-0/0 , 0-0-0/0 , 0-0-0/6 , 0-0-0/5 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/22 , 0-0-0/0 , 0-0-0/0
0-0-20/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/1 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/24 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/2 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_0.csv
_FILE-rung_1.csv
#VER=3.0
#LABEL=DCY
#COMMENT=Start sequential
#PREVRUNG=0
#NEXTRUNG=0
#NBRLINES=8
1-0-50/0 , 2-0-50/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/1
1-0-50/2 , 9-0-0/0 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/31 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_1.csv
_FILE-rung_7.csv
#VER=3.0
#LABEL=Motors
#COMMENT=Actions associated to steps
#PREVRUNG=-1
#NEXTRUNG=8
#NBRLINES=8
1-0-30/2 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/30
1-0-30/4 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-30/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/31
1-0-30/3 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/32
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_7.csv
_FILE-rung_8.csv
#VER=3.0
#LABEL=
#COMMENT=
#PREVRUNG=7
#NEXTRUNG=-1
#NBRLINES=8
1-0-30/7 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/33
1-0-30/8 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-30/9 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-30/5 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/34
1-0-30/6 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/35
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_8.csv
_FILE-sections.csv
#VER=1.0
#NAME000=Prelim
#NAME001=Grafcet
#NAME002=Post
000,0,-1,0,1,0
001,1,-1,0,0,0
002,0,-1,7,8,0
_/FILE-sections.csv
_FILE-sequential.csv
#VER=1.0
P0,sequential demo !
S0,1,0,0,1,1
S1,0,1,0,1,5
S2,0,2,0,1,7
S3,0,3,0,3,5
S4,0,4,0,3,7
S5,0,5,0,1,9
S6,0,6,0,3,9
S7,0,7,0,1,11
S8,0,8,0,1,3
S9,0,9,0,3,3
T0,8,9,-1,-1,-1,-1,-1,-1,-1,-1,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,2
T1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,1,3,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,6
T2,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,1,3,-1,-1,-1,-1,-1,-1,-1,-1,1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,3,6
T3,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,8
T4,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,3,8
T5,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,10
T6,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,3,10
T7,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,12
T8,1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,4
T9,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,9,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,3,4
C0,0,0/1
C1,0,0/2
C2,0,0/3
C3,0,0/4
C4,0,0/5
C5,0,0/6
C6,0,0/7
C7,0,0/8
C8,0,0/9
C9,0,0/10
N0,0,3,1,The first step
N1,0,6,3,Simultaneously
N2,0,4,11,The last step...
N3,0,6,6,Branch switch here !
_/FILE-sequential.csv
_FILE-symbols.csv
#VER=1.0
_/FILE-symbols.csv
_FILE-ioconf.csv
#VER=1.0
_/FILE-ioconf.csv
_FILE-com_params.txt
MODBUS_ELEMENT_OFFSET=1
MODBUS_MASTER_SERIAL_USE_RTS_TO_SEND=0
MODBUS_MASTER_TIME_INTER_FRAME=100
MODBUS_MASTER_TIME_OUT_RECEIPT=500
MODBUS_MASTER_TIME_AFTER_TRANSMIT=0
MODBUS_DEBUG_LEVEL=3
MODBUS_MAP_TYPE_FOR_READ_INPUTS=50
MODBUS_MAP_TYPE_FOR_READ_COILS=60
MODBUS_MAP_TYPE_FOR_WRITE_COILS=60
MODBUS_MAP_TYPE_FOR_READ_INPUT_REGS=270
MODBUS_MAP_TYPE_FOR_READ_HOLD_REGS=280
MODBUS_MAP_TYPE_FOR_WRITE_HOLD_REGS=280
_/FILE-com_params.txt
_FILE-modbusioconf.csv
#VER=2.0
_/FILE-modbusioconf.csv
_FILE-config_events.csv
#VER=1.0
_/FILE-config_events.csv
_FILE-modem_config.txt
MODEM_USED=0
INIT_SEQ=ATZ
CONFIG_SEQ=ATE0;ATS0=2
CALL_SEQ=ATDT
PIN_CODE=
_/FILE-modem_config.txt
_FILE-remote_alarms.txt
GLOBAL_ENABLED=0
SLOT_NAME_00=
ALARM_TYPE_00=-1
TELEPHONE_NUMBER_00=
EMAIL_ADDRESS_00=
SLOT_NAME_01=
ALARM_TYPE_01=-1
TELEPHONE_NUMBER_01=
EMAIL_ADDRESS_01=
SLOT_NAME_02=
ALARM_TYPE_02=-1
TELEPHONE_NUMBER_02=
EMAIL_ADDRESS_02=
SLOT_NAME_03=
ALARM_TYPE_03=-1
TELEPHONE_NUMBER_03=
EMAIL_ADDRESS_03=
SLOT_NAME_04=
ALARM_TYPE_04=-1
TELEPHONE_NUMBER_04=
EMAIL_ADDRESS_04=
SLOT_NAME_05=
ALARM_TYPE_05=-1
TELEPHONE_NUMBER_05=
EMAIL_ADDRESS_05=
SLOT_NAME_06=
ALARM_TYPE_06=-1
TELEPHONE_NUMBER_06=
EMAIL_ADDRESS_06=
SLOT_NAME_07=
ALARM_TYPE_07=-1
TELEPHONE_NUMBER_07=
EMAIL_ADDRESS_07=
CENTER_SERVER_SMS=
SMTP_SERVER_FOR_EMAILS=
SMTP_SERVER_USERNAME=
SMTP_SERVER_PASSWORD=
EMAIL_SENDER_ADDRESS=
_/FILE-remote_alarms.txt
_FILE-spy_vars.csv
200,200,200,200,200,200,200,200,200,200
0,1,2,3,4,5,6,7,8,9
0,0,0
0,0,0,0,0,0,0,0,0,0
_/FILE-spy_vars.csv
_/FILES_CLASSICLADDER
