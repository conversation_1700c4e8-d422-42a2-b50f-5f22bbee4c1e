#
# matplc.conf - configuration file
#


# shared memory map section

[PLC]
# The PLC has four configuration settings: 
# 	globalmap_key, confmap_pg, globalmap_pg, sem_key

sem_key = 42

point B0	"B0" classicladder
point B1	"B1" classicladder
point B2	"B2" classicladder
point B3	"B3" classicladder
point B4	"B4" classicladder


# specify periods for all the modules, so they don't hog the CPU
# classicladder: scan_period = 0.01
