CLASSIC LADDER PROJECT
Copyright (C) 2001-2023 <PERSON>
marc . le - douarain /AT\ laposte \DOT/ net
http://www.sourceforge.net/projects/classicladder
http://sites.google.com/site/classicladder
---------------------
FUTURE / TODO LIST...
---------------------

- Application compilation on Gtk+ version 3 to continue by removing warnings with some old functions used.
  But KEEP Gtk+ version 2 compatibility for now!!! (use only new fonctions also available on latests Gtk+2 version, or conditional compilation)
=> replace 'gtk_table' per 'gtk_grid'...
=> 'gdk_pixbuf_new_from_inline' already deprecated in Gtk+2 (use GResources instead), ...

- Also use gtk_entry_set_width_chars() on some entries in config window for smaller widths.

- Tests with 'valgrind' to be done... + CppCheck static analyse results to see...

- Adjust drawing height of label/comment rung header, to size of the font selected.

- Optimize CPU usage: no periodic refresh if not running (in edit, send "update signal" redraw only when something done or if needed)

- Perhaps to have new targets in Makefile, to directly allow "make embedded_plc_x86", "make embedded_plc_arietta", "make_embedded_raspi" to avoid manual
  editing of Makefile before compilation for an embedded target. possible use of "undefine VAR"
  (In Makefile, rename "-Dxxxxxx_GPIO_ACCESS" in "-DTARGET_xxxxxx" ???, or add -DTARGET_EMBEDDED_xxxxxx)

- In sequential edit, display in status bar next action that should be done for and/or links...
  Complex case of multi steps activated/desactivated simultaneously (click on steps -or- steps AND only more LEFT/RIGHT, then transition but not reverse else
  bads things arriving... to see!)
  
- Switch all monitor task creations to use common functions...

- Fixing library broken when compiled for that...

- Bug "auto rung selected" (because of VScrollBar reset) if resizing window during edit... can't just use gtk_window_set_resizable() with back to default size...

- Directly in rungs for contacts to be able to "extract a bit of a word" & also in coils "set a bit of a word" with var format "v[i]:Xz".
  Example "%W20:X4" => bit 4 of %W20 ; "%W10[%W30]:X9" => bit 9 of %W10[%W30].
  => will require new rung file format VER4.0 (if used in project) with var "t/o[ti/oi]" => "t/o[ti/oi]:Xb"
- Easy possibilities in eval block to be able to copy some bits list in a word and extract a bit of a word...
  New functions %Wx:=CONV_TO_WORD(First%By,QuantityZ) Parameters constants or vars. and also %Bx:=CONV_TO_BIT(%Wy,NumBitZ)
  -OR- new character after first %B with bits quantity %Bx:z that "equals" a word (to read or write it)...??? (can also be indexed!!! %Bx[y]:z)

- Add new analog physical local inputs/outputs sub-tabs in the config ("Analog Physical Inputs %IW"/"Analog Physical Outputs %QW").
  to be used with analogRead(pin) & analogWrite(pin,value) functions of WiringPi library with RaspberryPi
- Possibility to define y = aX + b equates with a/b constants for analog inputs/outputs %IW/%QW read/write (physical & modbus).

- Registers: add a time/date for each value stored, that can be viewed later for content of each value.

- To do a .deb package (for Debian/Ubuntu).
- Do an easy Windows installer ? (well just 'one' archive now including gtk files library to extract, so quite easy...)

- Review all variables names to be in conformity with IEC61131, especially %Bxxx => %Mxxx ; %Wxxx => %MWxxx

- Network config window, adding 'ip dynamic' checkbox
  + "raspbian" config format possible in read/write... (only parts modifications on file read (downloaded in tmp), no integral rewrite from datas in memory)
- After set "config network" done, target should call script target "rc.inet1" ? (actually reboot PLC (x86) target to do manually required)

- SMS received commands could optionnally be read (to set a var, get PLC status informations)
- SMS read, can be used to confirm SMS alarm really received.
- Have log history of the SMS alarms sended (and received?).

- For remote alarms, add "UDP client text string" or others things (after existing SMS & emails) ?

- Error %Sxx(?) if failed to open serial modbus port + all %S10-%S19 slaves ?

- Modbus master not really ending after loading project without modbus
  (close current opened serial port before in a CloseHardware() function before loading another project).
- Modbus master can (define?) use the 16550 realtime driver on Xenomai (do new file serial_rt_xeno + functions SerialOpen_rt, Serial_Send_rt, ...).

- Modbus slave (called server in IP) should accept many clients at the same time (on the same listen port). one thread per client,
  or specifc Lunux fd use ?
- Modbus slave (called server in IP) could also run in serial if a serial port string parametred for it in config_gtk (with RTS possible).
- In Modbus slave/server can have offsets to get directly access to %I and %IW variables (for example if address > 20000...?!)

- New systems vars time sunset/rise (integers) and perhaps directly boolean 'day' when we are between both (usefull to set light the night!)
  (optional define, without FPU instructions usage for SX CPU...)

( - Add second serial slave monitor thread )

( - For modbus RS485 half-duplex (choice between rx/tx on the line), can use a x86 gpio with outb instead of RTS signal
  (usefull with simple 3 wires serial port without RTS signal on it...)
  => replace in Gtk Config "Use RTS signal to send oNo oYes" per "Drive signal to xmit datas oNo oUseSerialRTS ox86OutPort" )

- Persistance of vars possible (to select which before in a list), current values saved on rising edged of a %Sxx var?!
  at start/end of the store magic values to verify at load that file integrity is ok before reading it...
- Have "default value" + "save context value" of vars lists. At start, each variables in the first list start with its default value.

- Passwords. for monitor access? future SMS commands? differents access levels?

- Vertical display optimization (nbr blocks no more fixed) depending of the elements present on the rung (recent NbrLinesUsed in StrRung)
  (but during edit, always displaying current edit with nbr of lines maxi).
- 'Undo' available during edit...

- Log of integer variables (with a periodic time and mini delta value) stored (in RAM or directly) csv files.
- Directly graphical display (with a gtk component like gtkdatabox? or GtkPlot of gtkextra?) of logs of integer variables.

- Adding inputs mask filter value in ms unit (for all local physical inputs) in config input window.

- Svg/Png export, should call draw functions with a new option DRAW_FOR_EXPORT (used when expr not drawed entirely on bottom instead of right margin!)
- to add a button to display a readme file about the currently loaded ladder file. Then someone could
  write a description of a ladder program in a text editor and name it 'my_program.clp.txt' and the
  button would load it (Chris Morley suggestion). perhaps not really no more necessary since comment field added in project?
- For the library, defining 4 functions pointers (callbacks) for I/O access instead of some duplicated code?
- Modbus/UDP mode support for the server & distributed I/O.
- Perhaps bug in project split if empty file (or line?) ?
- Search/replace function on a variable, cross-referencing, ... things like that...?
- Contextual popup menu when clicking on an element (Heli idea) to search and also spy directly...
- Modifs EMC 2010_08_20 on many things to look at...
  -> VarFloatArray
- Float variables support (%F, %IF, %QF) + in the evaluator...
- Do not allow to set/reset state of (by clicking on) input/bits "used"... not easy to know.
- Extend the new monitor slave protocol to allow for "hot" rung/sequential modify?
- To have a new script/text language available for a section (lua? basic?)
 (perhaps review ladder blocks that should also "live" outside this langage? idea: tags those not found on ladder,
  and exec thems!?)

- Your ideas ( well in fact this list is already long! ;-) ), languages translations, improvements, bugs reports or fixes (?!?!), etc...
  Do not hesitate to send me an email ! ;-)


