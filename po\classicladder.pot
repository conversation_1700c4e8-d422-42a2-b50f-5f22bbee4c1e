# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-30 10:40+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=CHARSET\n"
"Content-Transfer-Encoding: 8bit\n"

#: classicladder.c:312
msgid "Stopped program - press run button to continue."
msgstr ""

#: classicladder.c:325
msgid "Started program - press stop to pause."
msgstr ""

#: classicladder.c:353
msgid "Freezed program - select run or run one cycle to continue."
msgstr ""

#: classicladder.c:363
msgid "Started program for one cycle..."
msgstr ""

#: classicladder.c:427
msgid "Reset logic data - Now running."
msgstr ""

#: classicladder.c:427
msgid "Reset logic data done."
msgstr ""

#: classicladder.c:745
msgid "Project loaded and running"
msgstr ""

#: classicladder.c:745 classicladder_gtk.c:757
msgid "Project failed to load..."
msgstr ""

#: edit.c:163 edit.c:255 edit_sequential.c:136 editproperties_gtk.c:132
#: symbols_gtk.c:211 vars_browser_gtk.c:161
msgid "Variable"
msgstr ""

#: edit.c:166
msgid "JumpToLabel"
msgstr ""

#: edit.c:170 edit.c:261
msgid "Sub-Routine"
msgstr ""

#: edit.c:176 edit.c:202
msgid "TimerNbr"
msgstr ""

#: edit.c:178 edit.c:187 edit.c:204 edit.c:266 edit.c:270 edit.c:279
#: editproperties_gtk.c:81 editproperties_gtk.c:170
msgid "Base"
msgstr ""

#: edit.c:180 edit.c:189 edit.c:197 edit.c:206 edit.c:266 edit.c:270 edit.c:275
#: edit.c:279
msgid "Preset"
msgstr ""

#: edit.c:185
msgid "MonostNbr"
msgstr ""

#: edit.c:195
msgid "CounterNbr"
msgstr ""

#: edit.c:209 editproperties_gtk.c:92 editproperties_gtk.c:177
msgid "TimerMode"
msgstr ""

#: edit.c:214
msgid "RegisterNbr"
msgstr ""

#: edit.c:216 editproperties_gtk.c:103 editproperties_gtk.c:184
msgid "RegisterMode"
msgstr ""

#: edit.c:221 edit.c:289 editproperties_gtk.c:132 editproperties_gtk.c:135
msgid "Expression"
msgstr ""

#: edit.c:258
msgid "Label"
msgstr ""

#: edit.c:279 edit.c:283
msgid "Mode"
msgstr ""

#: edit.c:384
msgid "Incompatible type of variable (must be an integer!)"
msgstr ""

#: edit.c:424
msgid "Expression too long"
msgstr ""

#: edit.c:441 manager_gtk.c:578 print_gtk.c:319 menu_and_toolbar_gtk.c:596
#: edit_gtk.c:782 classicladder_gtk.c:753 classicladder_gtk.c:786
#: classicladder_gtk.c:1148 classicladder_gtk.c:1710 log_events_gtk.c:296
msgid "Ok"
msgstr ""

#: edit.c:505
msgid "Incompatible type of variable for index (must be an integer!)"
msgstr ""

#: edit.c:511
msgid "Parser error for indexed variable !"
msgstr ""

#: edit.c:517
msgid "You must select a boolean variable !"
msgstr ""

#: edit.c:526
msgid "You must select a read/write variable for a coil!"
msgstr ""

#: edit.c:542 edit_sequential.c:180 symbols_gtk.c:140 spy_vars_gtk.c:655
#: search.c:439
msgid "Unknown variable..."
msgstr ""

#: edit.c:1300
msgid "No more free function block of this type available..."
msgstr ""

#: edit.c:1312
msgid "No more free arithmetic expression for this type available..."
msgstr ""

#: edit.c:1514
msgid "You clicked outside of the current rung actually selected..."
msgstr ""

#: edit_sequential.c:116 edit_sequential.c:158
msgid "True"
msgstr ""

#: edit_sequential.c:117
msgid "False"
msgstr ""

#: edit_sequential.c:127
msgid "Step Nbr"
msgstr ""

#: edit_sequential.c:129 menu_and_toolbar_gtk.c:154
msgid "Init. Step"
msgstr ""

#: edit_sequential.c:138
msgid "StepsToReset"
msgstr ""

#: edit_sequential.c:140
msgid "StepsToSet"
msgstr ""

#: edit_sequential.c:142
msgid "OrTransisStart"
msgstr ""

#: edit_sequential.c:144
msgid "OrTransisEnd"
msgstr ""

#: edit_sequential.c:148 symbols_gtk.c:211 menu_and_toolbar_gtk.c:162
#: edit_gtk.c:138 vars_browser_gtk.c:161
msgid "Comment"
msgstr ""

#: edit_sequential.c:579
msgid ""
"There is already a step to deactivate for this transition (clicked on top "
"part)..."
msgstr ""

#: edit_sequential.c:591
msgid ""
"There is already a step to activate for this transition (clicked on bottom "
"part)..."
msgstr ""

#: edit_sequential.c:607
msgid "Not selected first and last transitions to be joined !!??"
msgstr ""

#: edit_sequential.c:624
msgid "Unknown element type for Ele1"
msgstr ""

#: edit_sequential.c:638
msgid "First and last steps selected are not on the same line !!??"
msgstr ""

#: edit_sequential.c:655
msgid "First and last transitions selected are not on the same line !!??"
msgstr ""

#: edit_sequential.c:662
msgid "Unknown element type for Ele2"
msgstr ""

#: edit_sequential.c:711 edit_sequential.c:756
msgid "Error in selection or not possible..."
msgstr ""

#: edit_sequential.c:826
msgid "Not found at least 2 transitions linked..."
msgstr ""

#: edit_sequential.c:952
msgid "Sequential memory full for steps"
msgstr ""

#: edit_sequential.c:957 edit_sequential.c:999
msgid "There is already an element!"
msgstr ""

#: edit_sequential.c:964
msgid "A step can't be placed on even lines"
msgstr ""

#: edit_sequential.c:994
msgid "Sequential memory full for transition"
msgstr ""

#: edit_sequential.c:1006
msgid "A transition can't be placed on odd lines"
msgstr ""

#: edit_sequential.c:1020
msgid "Now select the transition."
msgstr ""

#: edit_sequential.c:1022
msgid "Now select the step that will be deactivated by this transition."
msgstr ""

#: edit_sequential.c:1022
msgid "Now select the step that will be activated by this transition."
msgstr ""

#: edit_sequential.c:1024
msgid "You haven't selected a step or a transition to link!!!"
msgstr ""

#: edit_sequential.c:1040
msgid "You haven't selected a transition and then the step to link!!!"
msgstr ""

#: edit_sequential.c:1077
msgid "Sequential memory full for comments"
msgstr ""

#: edit_sequential.c:1082
msgid "There is already an element on 4 horizontal blocks required!"
msgstr ""

#: edit_sequential.c:1087
msgid "Not enough room on the right here..."
msgstr ""

#: editproperties_gtk.c:299 manager_gtk.c:596
msgid "Properties"
msgstr ""

#: editproperties_gtk.c:354 spy_vars_gtk.c:888 vars_browser_gtk.c:171
msgid "Variables browser"
msgstr ""

#: editproperties_gtk.c:366 spy_vars_gtk.c:1110
msgid "Apply"
msgstr ""

#: manager_gtk.c:101 manager_gtk.c:566
msgid "Main"
msgstr ""

#: manager_gtk.c:115 manager_gtk.c:556
msgid "Sequential"
msgstr ""

#: manager_gtk.c:115 manager_gtk.c:554
msgid "Ladder"
msgstr ""

#: manager_gtk.c:239
msgid "This section name already exists or is incorrect !!!"
msgstr ""

#: manager_gtk.c:268
msgid "This sub-routine number for calls is already defined !!!"
msgstr ""

#: manager_gtk.c:276
msgid "Failed to add a new section. Full?"
msgstr ""

#: manager_gtk.c:292 manager_gtk.c:316 manager_gtk.c:373 manager_gtk.c:400
#: manager_gtk.c:425 config_gtk.c:1717 spy_vars_gtk.c:1056 edit_gtk.c:379
#: edit_gtk.c:400 edit_gtk.c:421 classicladder_gtk.c:942
#: classicladder_gtk.c:954
msgid "Not possible when connected to a remote target..."
msgstr ""

#: manager_gtk.c:296 manager_gtk.c:377 manager_gtk.c:404 manager_gtk.c:429
msgid "Not possible when program running..."
msgstr ""

#: manager_gtk.c:307
msgid "Add a new section..."
msgstr ""

#: manager_gtk.c:344
msgid "Modify current section"
msgstr ""

#: manager_gtk.c:386 menu_and_toolbar_gtk.c:55 classicladder_gtk.c:944
msgid "New"
msgstr ""

#: manager_gtk.c:386
msgid "Do you really want to delete the section ?"
msgstr ""

#: manager_gtk.c:390
msgid "You can not delete the last section..."
msgstr ""

#: manager_gtk.c:416
msgid "This section is already executed the first !"
msgstr ""

#: manager_gtk.c:441
msgid "This section is already executed the latest !"
msgstr ""

#: manager_gtk.c:533 manager_gtk.c:618
msgid "Language"
msgstr ""

#: manager_gtk.c:534
msgid "Main/Sub-Routine"
msgstr ""

#: manager_gtk.c:535 config_gtk.c:1533
msgid "Name"
msgstr ""

#: manager_gtk.c:592
msgid "Add section"
msgstr ""

#: manager_gtk.c:592
msgid "Add New Section"
msgstr ""

#: manager_gtk.c:593
msgid "Delete section"
msgstr ""

#: manager_gtk.c:593
msgid "Delete Section"
msgstr ""

#: manager_gtk.c:594
msgid "Move up"
msgstr ""

#: manager_gtk.c:594
msgid "Priority order Move up"
msgstr ""

#: manager_gtk.c:595
msgid "Move down"
msgstr ""

#: manager_gtk.c:595
msgid "Priority order Move down"
msgstr ""

#: manager_gtk.c:596
msgid "Section Properties"
msgstr ""

#: manager_gtk.c:618
msgid "Nbr"
msgstr ""

#: manager_gtk.c:618
msgid "Section Name"
msgstr ""

#: manager_gtk.c:618 config_gtk.c:396 config_gtk.c:1533
msgid "Type"
msgstr ""

#: manager_gtk.c:618
msgid "debug"
msgstr ""

#: manager_gtk.c:623 menu_and_toolbar_gtk.c:106
msgid "Sections Manager"
msgstr ""

#: config_gtk.c:50 config_gtk.c:670 config_gtk.c:1534
msgid "None"
msgstr ""

#: config_gtk.c:50
msgid "DirectPortAccess"
msgstr ""

#: config_gtk.c:50
msgid "DirectPortConfig"
msgstr ""

#: config_gtk.c:50
msgid "Raspberry_GPIO"
msgstr ""

#: config_gtk.c:50
msgid "Atmel_SAM_GPIO"
msgstr ""

#: config_gtk.c:67
msgid "ReadInputs (to %I)"
msgstr ""

#: config_gtk.c:67
msgid "WriteCoils (from %Q)"
msgstr ""

#: config_gtk.c:67
msgid "ReadInputRegs (to %IW)"
msgstr ""

#: config_gtk.c:67
msgid "WriteHoldRegs (from %QW)"
msgstr ""

#: config_gtk.c:67
msgid "ReadCoils (to %Q)"
msgstr ""

#: config_gtk.c:67
msgid "ReadHoldRegs (to %QW)"
msgstr ""

#: config_gtk.c:67
msgid "ReadStatus (to %IW)"
msgstr ""

#: config_gtk.c:67
msgid "Diagnostic (from %IW/to %QW - 1stEle=sub-code used)"
msgstr ""

#: config_gtk.c:108
msgid "Select font used to draw"
msgstr ""

#: config_gtk.c:126
msgid "Available only on newer major version of Gtk+..."
msgstr ""

#: config_gtk.c:152
#, c-format
msgid "Periodic Refresh Rate 'inputs scan' (milliseconds)"
msgstr ""

#: config_gtk.c:156
#, c-format
msgid "Periodic Refresh Rate 'logic' (milliseconds)"
msgstr ""

#: config_gtk.c:161
msgid "Nbr.rungs"
msgstr ""

#: config_gtk.c:161 config_gtk.c:211
msgid "used"
msgstr ""

#: config_gtk.c:161 config_gtk.c:165 config_gtk.c:169 config_gtk.c:173
#: config_gtk.c:177 config_gtk.c:181 config_gtk.c:185 config_gtk.c:190
#: config_gtk.c:194 config_gtk.c:198 config_gtk.c:202 config_gtk.c:206
#: config_gtk.c:211 config_gtk.c:215 config_gtk.c:220 config_gtk.c:224
msgid "current alloc"
msgstr ""

#: config_gtk.c:161 config_gtk.c:173 config_gtk.c:177 config_gtk.c:181
#: config_gtk.c:185 config_gtk.c:206 config_gtk.c:211 config_gtk.c:220
#: config_gtk.c:224
msgid "size"
msgstr ""

#: config_gtk.c:161 config_gtk.c:173 config_gtk.c:177 config_gtk.c:181
#: config_gtk.c:185 config_gtk.c:206 config_gtk.c:211 config_gtk.c:220
#: config_gtk.c:224
msgid "bytes"
msgstr ""

#: config_gtk.c:165
msgid "Nbr.Bits"
msgstr ""

#: config_gtk.c:169
msgid "Nbr.Words"
msgstr ""

#: config_gtk.c:173
msgid "Nbr.Counters"
msgstr ""

#: config_gtk.c:177
msgid "Nbr.Timers IEC"
msgstr ""

#: config_gtk.c:181
msgid "Nbr.Registers"
msgstr ""

#: config_gtk.c:185
msgid "Register list size"
msgstr ""

#: config_gtk.c:190
msgid "Nbr.Phys.Inputs"
msgstr ""

#: config_gtk.c:194
msgid "Nbr.Phys.Outputs"
msgstr ""

#: config_gtk.c:198
msgid "Nbr.Phys.Words.Inputs"
msgstr ""

#: config_gtk.c:202
msgid "Nbr.Phys.Words.Outputs"
msgstr ""

#: config_gtk.c:206
msgid "Nbr.Arithm.Expr."
msgstr ""

#: config_gtk.c:211
msgid "Nbr.Sections"
msgstr ""

#: config_gtk.c:215
msgid "Nbr.Symbols"
msgstr ""

#: config_gtk.c:220
msgid "Nbr.Timers"
msgstr ""

#: config_gtk.c:224
msgid "Nbr.Monostables"
msgstr ""

#: config_gtk.c:229
#, c-format
msgid "Current file project"
msgstr ""

#: config_gtk.c:233
#, c-format
msgid "Default startup project"
msgstr ""

#: config_gtk.c:237
#, c-format
msgid "Font description used to draw"
msgstr ""

#: config_gtk.c:248
msgid ""
"Use real physical & serial modbus inputs/outputs only on the embedded target "
"(not for GTK simul interface)"
msgstr ""

#: config_gtk.c:295
msgid "Use as default project"
msgstr ""

#: config_gtk.c:302
msgid "No default project"
msgstr ""

#: config_gtk.c:309
msgid "Font select"
msgstr ""

#: config_gtk.c:396
msgid "First %"
msgstr ""

#: config_gtk.c:396
msgid "PortAdr(0x)/SubDev"
msgstr ""

#: config_gtk.c:396
msgid "1stChannel/GPIO"
msgstr ""

#: config_gtk.c:396
msgid "NbrChannels/GPIOs"
msgstr ""

#: config_gtk.c:396 config_gtk.c:723
msgid "Logic"
msgstr ""

#: config_gtk.c:396
msgid "ConfigData"
msgstr ""

#: config_gtk.c:441
msgid "1st %I mapped"
msgstr ""

#: config_gtk.c:441
msgid "1st %Q mapped"
msgstr ""

#: config_gtk.c:498 config_gtk.c:799
msgid "Inverted"
msgstr ""

#: config_gtk.c:687
msgid "not defined"
msgstr ""

#: config_gtk.c:723 config_gtk.c:908
msgid "Slave No"
msgstr ""

#: config_gtk.c:723
msgid "Request Type"
msgstr ""

#: config_gtk.c:723
msgid "1st Modbus Ele."
msgstr ""

#: config_gtk.c:723
msgid "Nbr of Ele"
msgstr ""

#: config_gtk.c:723
msgid "1st I/Q/IW/QW mapped"
msgstr ""

#: config_gtk.c:898
msgid "Overflow error for I,Q,B,IQ,WQ or W mapping detected..."
msgstr ""

#: config_gtk.c:908
msgid "Slave Address"
msgstr ""

#: config_gtk.c:908
msgid "TCP/UDP mode"
msgstr ""

#: config_gtk.c:908
msgid "Module Information"
msgstr ""

#: config_gtk.c:957
msgid "UDP instead of TCP"
msgstr ""

#: config_gtk.c:993
msgid "SerialAdr -or- AdrIP -or- AdrIP:Port"
msgstr ""

#: config_gtk.c:1101
#, c-format
msgid "Modbus master Serial port (blank = IP mode)"
msgstr ""

#: config_gtk.c:1105
#, c-format
msgid "Serial baud rate"
msgstr ""

#: config_gtk.c:1109
#, c-format
msgid "Serial nbr. data bits"
msgstr ""

#: config_gtk.c:1113
#, c-format
msgid "Serial parity"
msgstr ""

#: config_gtk.c:1117
#, c-format
msgid "Serial nbr. stops bits"
msgstr ""

#: config_gtk.c:1121
#, c-format
msgid "After transmit pause - milliseconds"
msgstr ""

#: config_gtk.c:1125
#, c-format
msgid "After receive pause - milliseconds"
msgstr ""

#: config_gtk.c:1129
#, c-format
msgid "Request Timeout length - milliseconds"
msgstr ""

#: config_gtk.c:1133
#, c-format
msgid "Use RTS signal to send"
msgstr ""

#: config_gtk.c:1137
#, c-format
msgid "Modbus element offset"
msgstr ""

#: config_gtk.c:1141
#, c-format
msgid "Debug level"
msgstr ""

#: config_gtk.c:1142
msgid "QUIET"
msgstr ""

#: config_gtk.c:1142
msgid "LEVEL"
msgstr ""

#: config_gtk.c:1142
msgid "VERBOSE"
msgstr ""

#: config_gtk.c:1145
#, c-format
msgid "Read inputs map to"
msgstr ""

#: config_gtk.c:1149
#, c-format
msgid "Read coils map to"
msgstr ""

#: config_gtk.c:1153
#, c-format
msgid "Write coils map from"
msgstr ""

#: config_gtk.c:1157
#, c-format
msgid "Read input registers map to"
msgstr ""

#: config_gtk.c:1161
#, c-format
msgid "Read hold registers map to"
msgstr ""

#: config_gtk.c:1165
#, c-format
msgid "Write hold registers map from"
msgstr ""

#: config_gtk.c:1260
msgid "1st %Bxxxx"
msgstr ""

#: config_gtk.c:1260
msgid "Nbr Of %B"
msgstr ""

#: config_gtk.c:1260 log_events_gtk.c:327
msgid "Symbol"
msgstr ""

#: config_gtk.c:1260
msgid "Text event"
msgstr ""

#: config_gtk.c:1260
msgid "Level(>0=Def)"
msgstr ""

#: config_gtk.c:1260
msgid "Forward Remote Alarms Slots"
msgstr ""

#: config_gtk.c:1423
msgid "Overflow error for first/nbrs detected..."
msgstr ""

#: config_gtk.c:1439
msgid "Modem on slave monitor"
msgstr ""

#: config_gtk.c:1439
msgid "AT init sequence"
msgstr ""

#: config_gtk.c:1439
msgid "AT config sequence"
msgstr ""

#: config_gtk.c:1439
msgid "AT call sequence"
msgstr ""

#: config_gtk.c:1439
msgid "Optional PIN Code"
msgstr ""

#: config_gtk.c:1448
msgid "Automatically adjust summer/winter time."
msgstr ""

#: config_gtk.c:1460
msgid "Use"
msgstr ""

#: config_gtk.c:1489
msgid "--- Monitor Master modem AT sequences ---"
msgstr ""

#: config_gtk.c:1532
msgid "Global remote alarms enable"
msgstr ""

#: config_gtk.c:1532
msgid "Slot Alarms"
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '0': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '1': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '2': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '3': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '4': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '5': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '6': "
msgstr ""

#: config_gtk.c:1532
msgid "Remote Slot '7': "
msgstr ""

#: config_gtk.c:1532
msgid "Center SMS Server"
msgstr ""

#: config_gtk.c:1532
msgid "Smtp Server For Emails"
msgstr ""

#: config_gtk.c:1532
msgid "Smtp Server User Name"
msgstr ""

#: config_gtk.c:1532
msgid "Smtp Server Password"
msgstr ""

#: config_gtk.c:1532
msgid "Email Sender Address"
msgstr ""

#: config_gtk.c:1533
msgid "Telephone (SMS)"
msgstr ""

#: config_gtk.c:1533 config_gtk.c:1534
msgid "Email"
msgstr ""

#: config_gtk.c:1534
msgid "SMS"
msgstr ""

#: config_gtk.c:1730
msgid "Period/Sizes/Info"
msgstr ""

#: config_gtk.c:1733
msgid "Events Config"
msgstr ""

#: config_gtk.c:1737
msgid "Physical Inputs %I"
msgstr ""

#: config_gtk.c:1739
msgid "Physical Outputs %Q"
msgstr ""

#: config_gtk.c:1740
msgid "Physical Inputs/Outputs"
msgstr ""

#: config_gtk.c:1743
msgid "Modbus communication"
msgstr ""

#: config_gtk.c:1745
msgid "Modbus slaves"
msgstr ""

#: config_gtk.c:1747
msgid "Modbus I/O"
msgstr ""

#: config_gtk.c:1750
msgid "Remote Alarms"
msgstr ""

#: config_gtk.c:1752
msgid "Misc/Modem"
msgstr ""

#: vars_names.c:624
msgid "Unknown variable (number value out of bound)"
msgstr ""

#: symbols_gtk.c:120
#, c-format
msgid "A variable name always start with '%' character !"
msgstr ""

#: symbols_gtk.c:211 vars_browser_gtk.c:161
msgid "Symbol name"
msgstr ""

#: symbols_gtk.c:214
msgid "Symbols names"
msgstr ""

#: spy_vars_gtk.c:128
msgid "Set/UnSet variable"
msgstr ""

#: spy_vars_gtk.c:131
msgid "Set to 1"
msgstr ""

#: spy_vars_gtk.c:132
msgid "Set to 0"
msgstr ""

#: spy_vars_gtk.c:133
msgid "UnSet"
msgstr ""

#: spy_vars_gtk.c:380
msgid "Spy bools vars"
msgstr ""

#: spy_vars_gtk.c:388 classicladder_gtk.c:1518
msgid "Display symbols"
msgstr ""

#: spy_vars_gtk.c:411
msgid "Offset for vars displayed below (press return to apply)"
msgstr ""

#: spy_vars_gtk.c:434
msgid "Set/Unset output"
msgstr ""

#: spy_vars_gtk.c:434
msgid "Set/Unset input"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Sunday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Monday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Tuesday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Wednesday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Thursday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Friday"
msgstr ""

#: spy_vars_gtk.c:727
msgid "Saturday"
msgstr ""

#: spy_vars_gtk.c:826
msgid "Dec +/-"
msgstr ""

#: spy_vars_gtk.c:827
msgid "Dec +"
msgstr ""

#: spy_vars_gtk.c:828
msgid "Hex"
msgstr ""

#: spy_vars_gtk.c:829
msgid "Bin"
msgstr ""

#: spy_vars_gtk.c:860
msgid "Modify Value :"
msgstr ""

#: spy_vars_gtk.c:956
msgid "Not connected"
msgstr ""

#: spy_vars_gtk.c:977
msgid "Used"
msgstr ""

#: spy_vars_gtk.c:978
msgid "Free"
msgstr ""

#: spy_vars_gtk.c:979
msgid "Size"
msgstr ""

#: spy_vars_gtk.c:1022
msgid "ClassicLadder Soft.Version"
msgstr ""

#: spy_vars_gtk.c:1022
msgid "Kernel Version"
msgstr ""

#: spy_vars_gtk.c:1022
msgid "Xenomai version"
msgstr ""

#: spy_vars_gtk.c:1022
msgid "Linux Distribution"
msgstr ""

#: spy_vars_gtk.c:1022
msgid "Disk statistics"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Project Name"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Project Site"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Author"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Company"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Param.Version"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Creation Date"
msgstr ""

#: spy_vars_gtk.c:1084
msgid "Modify Date"
msgstr ""

#: spy_vars_gtk.c:1123
msgid "Spy free vars"
msgstr ""

#: spy_vars_gtk.c:1127
msgid "Vars & Time"
msgstr ""

#: spy_vars_gtk.c:1129
msgid "Project properties"
msgstr ""

#: spy_vars_gtk.c:1131
msgid "Target infos"
msgstr ""

#: print_gtk.c:162
msgid "Symbols list"
msgstr ""

#: print_gtk.c:162 print_gtk.c:208
msgid "Page"
msgstr ""

#: print_gtk.c:208
msgid "Section"
msgstr ""

#: print_gtk.c:274
msgid "Print section"
msgstr ""

#: print_gtk.c:278
msgid "Print only current section"
msgstr ""

#: print_gtk.c:279
msgid "Print all the sections"
msgstr ""

#: print_gtk.c:282
msgid "Print symbols list"
msgstr ""

#: print_gtk.c:303
msgid "ClassicLadder Options"
msgstr ""

#: print_gtk.c:319 menu_and_toolbar_gtk.c:64
msgid "Print"
msgstr ""

#: print_gtk.c:319
msgid "Failed to print..."
msgstr ""

#: menu_and_toolbar_gtk.c:54 classicladder_gtk.c:1387
msgid "File"
msgstr ""

#: menu_and_toolbar_gtk.c:55
msgid "Create a new project"
msgstr ""

#: menu_and_toolbar_gtk.c:56
msgid "Load"
msgstr ""

#: menu_and_toolbar_gtk.c:56
msgid "Load an existing project"
msgstr ""

#: menu_and_toolbar_gtk.c:57 menu_and_toolbar_gtk.c:149
msgid "Save"
msgstr ""

#: menu_and_toolbar_gtk.c:57
msgid "Save current project"
msgstr ""

#: menu_and_toolbar_gtk.c:58
msgid "Save As..."
msgstr ""

#: menu_and_toolbar_gtk.c:58
msgid "Save project to another file"
msgstr ""

#: menu_and_toolbar_gtk.c:59
msgid "Export to"
msgstr ""

#: menu_and_toolbar_gtk.c:62
msgid "Clipboard"
msgstr ""

#: menu_and_toolbar_gtk.c:63
msgid "Preview"
msgstr ""

#: menu_and_toolbar_gtk.c:64
msgid "Print current section"
msgstr ""

#: menu_and_toolbar_gtk.c:65
msgid "Quit"
msgstr ""

#: menu_and_toolbar_gtk.c:67
msgid "View"
msgstr ""

#: menu_and_toolbar_gtk.c:69
msgid "Register block content"
msgstr ""

#: menu_and_toolbar_gtk.c:69
msgid "View register block content"
msgstr ""

#: menu_and_toolbar_gtk.c:70
msgid "Frames log windows"
msgstr ""

#: menu_and_toolbar_gtk.c:71
msgid "Linux SysLog debug"
msgstr ""

#: menu_and_toolbar_gtk.c:71
msgid "View Linux SysLog debug"
msgstr ""

#: menu_and_toolbar_gtk.c:73
msgid "Search"
msgstr ""

#: menu_and_toolbar_gtk.c:74
msgid "Find"
msgstr ""

#: menu_and_toolbar_gtk.c:74
msgid "Find First"
msgstr ""

#: menu_and_toolbar_gtk.c:75
msgid "Find Next"
msgstr ""

#: menu_and_toolbar_gtk.c:76
msgid "Find Previous"
msgstr ""

#: menu_and_toolbar_gtk.c:76
msgid "Find Down"
msgstr ""

#: menu_and_toolbar_gtk.c:77
msgid "Go to First Rung"
msgstr ""

#: menu_and_toolbar_gtk.c:78
msgid "Go to Last Rung"
msgstr ""

#: menu_and_toolbar_gtk.c:79
msgid "Go to Previous Section"
msgstr ""

#: menu_and_toolbar_gtk.c:80
msgid "Go to Next Section"
msgstr ""

#: menu_and_toolbar_gtk.c:82
msgid "PLC"
msgstr ""

#: menu_and_toolbar_gtk.c:86 menu_and_toolbar_gtk.c:478
#: menu_and_toolbar_gtk.c:485
msgid "Run logic"
msgstr ""

#: menu_and_toolbar_gtk.c:86
msgid "Start/stop logic"
msgstr ""

#: menu_and_toolbar_gtk.c:87
msgid "Run logic only one cycle"
msgstr ""

#: menu_and_toolbar_gtk.c:87
msgid "Run logic one cycle/freeze logic"
msgstr ""

#: menu_and_toolbar_gtk.c:88
msgid "Reset logic"
msgstr ""

#: menu_and_toolbar_gtk.c:89
msgid "Configuration"
msgstr ""

#: menu_and_toolbar_gtk.c:89
msgid "Configuration (sizes, i/o, ...)"
msgstr ""

#: menu_and_toolbar_gtk.c:91
msgid "Set Target Clock Time"
msgstr ""

#: menu_and_toolbar_gtk.c:91
msgid "Set Clock Time of the Target with PC Time"
msgstr ""

#: menu_and_toolbar_gtk.c:92
msgid "Reboot/Halt Target"
msgstr ""

#: menu_and_toolbar_gtk.c:93
msgid "Reboot Target"
msgstr ""

#: menu_and_toolbar_gtk.c:93
msgid "Ask to reboot the target"
msgstr ""

#: menu_and_toolbar_gtk.c:94
msgid "Halt Target"
msgstr ""

#: menu_and_toolbar_gtk.c:94
msgid "Ask to halt the target"
msgstr ""

#: menu_and_toolbar_gtk.c:95
msgid "Target network config"
msgstr ""

#: menu_and_toolbar_gtk.c:95
msgid "See and modify target IP network parameters"
msgstr ""

#: menu_and_toolbar_gtk.c:96
msgid "Target monitor serial config"
msgstr ""

#: menu_and_toolbar_gtk.c:96
msgid "See and modify target monitor serial config"
msgstr ""

#: menu_and_toolbar_gtk.c:97
msgid "File Transfer"
msgstr ""

#: menu_and_toolbar_gtk.c:98
msgid "Send current project to Target"
msgstr ""

#: menu_and_toolbar_gtk.c:99
msgid "Receive project of Target"
msgstr ""

#: menu_and_toolbar_gtk.c:100
msgid "Send update soft archive to Target"
msgstr ""

#: menu_and_toolbar_gtk.c:103
msgid "Help"
msgstr ""

#: menu_and_toolbar_gtk.c:104
msgid "About"
msgstr ""

#: menu_and_toolbar_gtk.c:106
msgid "Open Sections Manager Window"
msgstr ""

#: menu_and_toolbar_gtk.c:108
msgid "Add rung (alt-a)"
msgstr ""

#: menu_and_toolbar_gtk.c:108
msgid "Add rung"
msgstr ""

#: menu_and_toolbar_gtk.c:109
msgid "Insert rung (alt-i)"
msgstr ""

#: menu_and_toolbar_gtk.c:109
msgid "Insert rung"
msgstr ""

#: menu_and_toolbar_gtk.c:110
msgid "Delete rung (alt-x)"
msgstr ""

#: menu_and_toolbar_gtk.c:110
msgid "Delete rung"
msgstr ""

#: menu_and_toolbar_gtk.c:111
msgid "Modify (alt-m)"
msgstr ""

#: menu_and_toolbar_gtk.c:111 edit_gtk.c:777
msgid "Modify"
msgstr ""

#: menu_and_toolbar_gtk.c:113
msgid "Pointer (alt-p)"
msgstr ""

#: menu_and_toolbar_gtk.c:113
msgid "Pointer"
msgstr ""

#: menu_and_toolbar_gtk.c:114
msgid "Eraser (alt-x)"
msgstr ""

#: menu_and_toolbar_gtk.c:114
msgid "Erase"
msgstr ""

#: menu_and_toolbar_gtk.c:115
msgid "Draw H line (alt-h)"
msgstr ""

#: menu_and_toolbar_gtk.c:115
msgid "Draw H line"
msgstr ""

#: menu_and_toolbar_gtk.c:116
msgid "Draw H line to end (alt-l)"
msgstr ""

#: menu_and_toolbar_gtk.c:116
msgid "Draw H line to end"
msgstr ""

#: menu_and_toolbar_gtk.c:117
msgid "Connect V line (alt-v)"
msgstr ""

#: menu_and_toolbar_gtk.c:117
msgid "Connect V line"
msgstr ""

#: menu_and_toolbar_gtk.c:119 search.c:45
msgid "Contacts"
msgstr ""

#: menu_and_toolbar_gtk.c:120
msgid "Open contact (alt-i)"
msgstr ""

#: menu_and_toolbar_gtk.c:120
msgid "Open contact"
msgstr ""

#: menu_and_toolbar_gtk.c:121
msgid "Closed contact"
msgstr ""

#: menu_and_toolbar_gtk.c:122
msgid "Rising edge"
msgstr ""

#: menu_and_toolbar_gtk.c:123
msgid "Falling edge"
msgstr ""

#: menu_and_toolbar_gtk.c:125 search.c:45
msgid "Coils"
msgstr ""

#: menu_and_toolbar_gtk.c:126
msgid "Coil (alt-o)"
msgstr ""

#: menu_and_toolbar_gtk.c:126
msgid "Coil"
msgstr ""

#: menu_and_toolbar_gtk.c:127
msgid "Inverted coil"
msgstr ""

#: menu_and_toolbar_gtk.c:128
msgid "Set"
msgstr ""

#: menu_and_toolbar_gtk.c:129
msgid "Reset"
msgstr ""

#: menu_and_toolbar_gtk.c:130
msgid "Jump"
msgstr ""

#: menu_and_toolbar_gtk.c:131
msgid "Call"
msgstr ""

#: menu_and_toolbar_gtk.c:133
msgid "Boxes"
msgstr ""

#: menu_and_toolbar_gtk.c:134
msgid "IEC timer"
msgstr ""

#: menu_and_toolbar_gtk.c:135
msgid "Counter"
msgstr ""

#: menu_and_toolbar_gtk.c:137
msgid "Old timer"
msgstr ""

#: menu_and_toolbar_gtk.c:138
msgid "Old mono"
msgstr ""

#: menu_and_toolbar_gtk.c:140 search.c:45
msgid "Compare"
msgstr ""

#: menu_and_toolbar_gtk.c:141 search.c:45
msgid "Operate"
msgstr ""

#: menu_and_toolbar_gtk.c:143
msgid "Actions"
msgstr ""

#: menu_and_toolbar_gtk.c:144
msgid "Element invert"
msgstr ""

#: menu_and_toolbar_gtk.c:145
msgid "Block Select"
msgstr ""

#: menu_and_toolbar_gtk.c:145
msgid "Select"
msgstr ""

#: menu_and_toolbar_gtk.c:146
msgid "Block Copy"
msgstr ""

#: menu_and_toolbar_gtk.c:146
msgid "Copy"
msgstr ""

#: menu_and_toolbar_gtk.c:147
msgid "Block Move"
msgstr ""

#: menu_and_toolbar_gtk.c:147
msgid "Move"
msgstr ""

#: menu_and_toolbar_gtk.c:149
msgid "Save (alt-Return)"
msgstr ""

#: menu_and_toolbar_gtk.c:150
msgid "Cancel (alt-c)"
msgstr ""

#: menu_and_toolbar_gtk.c:150 edit_gtk.c:786
msgid "Cancel"
msgstr ""

#: menu_and_toolbar_gtk.c:153 edit_gtk.c:133
msgid "Step"
msgstr ""

#: menu_and_toolbar_gtk.c:155 edit_gtk.c:134
msgid "Transition"
msgstr ""

#: menu_and_toolbar_gtk.c:156
msgid "Step And Transition"
msgstr ""

#: menu_and_toolbar_gtk.c:157
msgid "Transitions Or Start"
msgstr ""

#: menu_and_toolbar_gtk.c:158
msgid "Transitions Or End"
msgstr ""

#: menu_and_toolbar_gtk.c:159
msgid "Steps And Start"
msgstr ""

#: menu_and_toolbar_gtk.c:160
msgid "Steps And End"
msgstr ""

#: menu_and_toolbar_gtk.c:161 edit_gtk.c:137
msgid "Link"
msgstr ""

#: menu_and_toolbar_gtk.c:166
msgid "Sections window"
msgstr ""

#: menu_and_toolbar_gtk.c:166
msgid "View sections manager window"
msgstr ""

#: menu_and_toolbar_gtk.c:167
msgid "Editor window"
msgstr ""

#: menu_and_toolbar_gtk.c:167
msgid "View editor window"
msgstr ""

#: menu_and_toolbar_gtk.c:168
msgid "Symbols window"
msgstr ""

#: menu_and_toolbar_gtk.c:168
msgid "View symbols window"
msgstr ""

#: menu_and_toolbar_gtk.c:169
msgid "Bools vars window"
msgstr ""

#: menu_and_toolbar_gtk.c:170
msgid "Free vars window"
msgstr ""

#: menu_and_toolbar_gtk.c:172
msgid "Events log window"
msgstr ""

#: menu_and_toolbar_gtk.c:174
msgid "Monitor master frames with target"
msgstr ""

#: menu_and_toolbar_gtk.c:175
msgid "Modbus master frames"
msgstr ""

#: menu_and_toolbar_gtk.c:176
msgid "Target monitor slave (IP) frames"
msgstr ""

#: menu_and_toolbar_gtk.c:177
msgid "Target monitor slave (Serial) frames"
msgstr ""

#: menu_and_toolbar_gtk.c:178
msgid "Modbus slave frames"
msgstr ""

#: menu_and_toolbar_gtk.c:478 menu_and_toolbar_gtk.c:485
msgid "Stop logic"
msgstr ""

#: menu_and_toolbar_gtk.c:480
msgid "Freeze logic"
msgstr ""

#: menu_and_toolbar_gtk.c:480
msgid "Run logic one cycle"
msgstr ""

#: menu_and_toolbar_gtk.c:484
msgid "Stop"
msgstr ""

#: menu_and_toolbar_gtk.c:484
msgid "Run"
msgstr ""

#: menu_and_toolbar_gtk.c:493
msgid "Disconnect"
msgstr ""

#: menu_and_toolbar_gtk.c:493
msgid "Connect"
msgstr ""

#: menu_and_toolbar_gtk.c:502 menu_and_toolbar_gtk.c:512
#: menu_and_toolbar_gtk.c:519 network_config_window_gtk.c:56
#: monitor_serial_config_window_gtk.c:57
msgid "You are not currently connected to a remote target..."
msgstr ""

#: menu_and_toolbar_gtk.c:545
msgid "Please select the update soft archive to send"
msgstr ""

#: menu_and_toolbar_gtk.c:563
msgid "Register content"
msgstr ""

#: menu_and_toolbar_gtk.c:563
msgid "Select register number to view"
msgstr ""

#: menu_and_toolbar_gtk.c:596
msgid "Register selection error"
msgstr ""

#: menu_and_toolbar_gtk.c:596
msgid "This register is not defined..."
msgstr ""

#: monitor_windows_gtk.c:97
msgid ""
"Already in communication with the remote target (monitor or file transfer)..."
msgstr ""

#: monitor_windows_gtk.c:101
msgid "Target to connect"
msgstr ""

#: monitor_windows_gtk.c:101
msgid "File transfer"
msgstr ""

#: monitor_windows_gtk.c:114
msgid "IP network"
msgstr ""

#: monitor_windows_gtk.c:118
msgid "Serial link"
msgstr ""

#: monitor_windows_gtk.c:122
msgid "Modem"
msgstr ""

#: monitor_windows_gtk.c:130
msgid "IP address or hostname"
msgstr ""

#: monitor_windows_gtk.c:141
msgid "Serial port"
msgstr ""

#: monitor_windows_gtk.c:148
msgid "Speed"
msgstr ""

#: monitor_windows_gtk.c:160
msgid "Telephone number"
msgstr ""

#: monitor_windows_gtk.c:169
msgid "Reply timeout (ms)"
msgstr ""

#: monitor_windows_gtk.c:322
msgid "Asked to read frames log buffer file of the target..."
msgstr ""

#: monitor_windows_gtk.c:407
msgid "Modbus master frames (buffered)"
msgstr ""

#: monitor_windows_gtk.c:408
msgid "Target monitor slave (IP) frames (buffered)"
msgstr ""

#: monitor_windows_gtk.c:409
msgid "Target monitor slave (Serial) frames (buffered)"
msgstr ""

#: monitor_windows_gtk.c:410
msgid "Modbus slave/server (IP) frames (buffered)"
msgstr ""

#: monitor_windows_gtk.c:411
msgid "Monitor master frames with target (live)"
msgstr ""

#: monitor_windows_gtk.c:439 log_events_gtk.c:368
msgid "Refresh"
msgstr ""

#: monitor_windows_gtk.c:445
msgid "Scroll"
msgstr ""

#: monitor_windows_gtk.c:449
msgid "CleanUp"
msgstr ""

#: monitor_windows_gtk.c:462
msgid "Copy to clipboard"
msgstr ""

#: monitor_windows_gtk.c:480
msgid "Stats for modbus slave:"
msgstr ""

#: monitor_windows_gtk.c:648
msgid "Failed to load frames log file."
msgstr ""

#: search.c:45
msgid "All except blocks"
msgstr ""

#: search.c:45
msgid "Transitions"
msgstr ""

#: search.c:45
msgid "Blocks"
msgstr ""

#: search.c:398
msgid "### NOT FOUND ###"
msgstr ""

#: network_config_window_gtk.c:47
msgid "IP Ad."
msgstr ""

#: network_config_window_gtk.c:47
msgid "Mask"
msgstr ""

#: network_config_window_gtk.c:47
msgid "Route"
msgstr ""

#: network_config_window_gtk.c:47
msgid "Server DNS 1"
msgstr ""

#: network_config_window_gtk.c:47
msgid "Server DNS 2"
msgstr ""

#: network_config_window_gtk.c:47
msgid "HostName"
msgstr ""

#: network_config_window_gtk.c:61
msgid "Network config"
msgstr ""

#: network_config_window_gtk.c:80 monitor_serial_config_window_gtk.c:79
msgid "reading..."
msgstr ""

#: monitor_serial_config_window_gtk.c:47
msgid "Serial port (blank=not used)"
msgstr ""

#: monitor_serial_config_window_gtk.c:47
msgid "Serial Speed"
msgstr ""

#: monitor_serial_config_window_gtk.c:62
msgid "Serial monitor config"
msgstr ""

#: edit_gtk.c:98 edit_gtk.c:132
msgid ""
"Current Object\n"
"Selector"
msgstr ""

#: edit_gtk.c:98 edit_gtk.c:132
msgid "Eraser"
msgstr ""

#: edit_gtk.c:98
msgid ""
"Invert logic\n"
"of object"
msgstr ""

#: edit_gtk.c:99
msgid ""
"Select a rung part\n"
"(drag and release)"
msgstr ""

#: edit_gtk.c:99
msgid ""
"Copy rung part\n"
"selected"
msgstr ""

#: edit_gtk.c:99
msgid ""
"Move rung part\n"
"selected"
msgstr ""

#: edit_gtk.c:100
msgid "N.O. Input"
msgstr ""

#: edit_gtk.c:100
msgid "N.C. Input"
msgstr ""

#: edit_gtk.c:100
msgid ""
"Rising Edge\n"
" Input"
msgstr ""

#: edit_gtk.c:100
msgid ""
"Falling Edge\n"
" Input"
msgstr ""

#: edit_gtk.c:101
msgid ""
"Horizontal\n"
"Connection"
msgstr ""

#: edit_gtk.c:101
msgid ""
"Vertical\n"
"Connection"
msgstr ""

#: edit_gtk.c:101
msgid ""
"Long Horizontal\n"
"Connection"
msgstr ""

#: edit_gtk.c:102
msgid "Timer IEC Block"
msgstr ""

#: edit_gtk.c:102
msgid "Counter Block"
msgstr ""

#: edit_gtk.c:102
msgid "Register Block"
msgstr ""

#: edit_gtk.c:102
msgid ""
"Variable\n"
"Comparison"
msgstr ""

#: edit_gtk.c:104
msgid "Old Timer Block"
msgstr ""

#: edit_gtk.c:104
msgid "Old Monostable Block"
msgstr ""

#: edit_gtk.c:106
msgid "N.O. Output"
msgstr ""

#: edit_gtk.c:106
msgid "N.C. Output"
msgstr ""

#: edit_gtk.c:106
msgid "Set Output"
msgstr ""

#: edit_gtk.c:106
msgid "Reset Output"
msgstr ""

#: edit_gtk.c:107
msgid "Jump Coil"
msgstr ""

#: edit_gtk.c:107
msgid "Call Coil"
msgstr ""

#: edit_gtk.c:107
msgid ""
"Variable\n"
"Assignment"
msgstr ""

#: edit_gtk.c:133
msgid "Init Step (activated at start)"
msgstr ""

#: edit_gtk.c:134
msgid "Step and Transition (shortcut)"
msgstr ""

#: edit_gtk.c:135
msgid "Transitions start switch (or)"
msgstr ""

#: edit_gtk.c:135
msgid "Transitions end switch (or)"
msgstr ""

#: edit_gtk.c:136
msgid "Activate many steps (start)"
msgstr ""

#: edit_gtk.c:136
msgid "Deactivate many steps (end)"
msgstr ""

#: edit_gtk.c:320
msgid "Current rung in edit mode..."
msgstr ""

#: edit_gtk.c:320
msgid "Edit mode..."
msgstr ""

#: edit_gtk.c:392
msgid "Failed to add a new rung. Full?"
msgstr ""

#: edit_gtk.c:413
msgid "Failed to insert a new rung. Full?"
msgstr ""

#: edit_gtk.c:425 edit_gtk.c:772
msgid "Delete"
msgstr ""

#: edit_gtk.c:425
msgid "Do you really want to delete the current rung ?"
msgstr ""

#: edit_gtk.c:432
msgid "Actually, not possible on a connected remote target..."
msgstr ""

#: edit_gtk.c:483 classicladder_gtk.c:956 log_events_gtk.c:317
msgid "Sure?"
msgstr ""

#: edit_gtk.c:483
msgid ""
"Do you really want to cancel ?\n"
"(all current modifications will be lost...)"
msgstr ""

#: edit_gtk.c:624
msgid "No rung part previously selected before copying or moving it..."
msgstr ""

#: edit_gtk.c:756
msgid "Editor"
msgstr ""

#: edit_gtk.c:762
msgid "Add"
msgstr ""

#: edit_gtk.c:767
msgid "Insert"
msgstr ""

#: classicladder_gtk.c:753
msgid "Load Error"
msgstr ""

#: classicladder_gtk.c:753
msgid "Failed to load the project file..."
msgstr ""

#: classicladder_gtk.c:757
msgid "Project loaded (stopped)."
msgstr ""

#: classicladder_gtk.c:786 log_events_gtk.c:296
msgid "Save Error"
msgstr ""

#: classicladder_gtk.c:786
msgid "Failed to save the project file..."
msgstr ""

#: classicladder_gtk.c:798 classicladder_gtk.c:969 classicladder_gtk.c:1272
#: classicladder_gtk.c:1274 classicladder_gtk.c:1276
msgid "Warning!"
msgstr ""

#: classicladder_gtk.c:798
msgid ""
"You are currently under edit.\n"
"You should apply current modifications before...\n"
"Do you really want to save now ?\n"
msgstr ""

#: classicladder_gtk.c:878
msgid "ClassicLadder softs archives"
msgstr ""

#: classicladder_gtk.c:886
msgid "Old directories projects"
msgstr ""

#: classicladder_gtk.c:890
msgid "ClassicLadder projects"
msgstr ""

#: classicladder_gtk.c:944
msgid "Do you really want to clear all datas ?"
msgstr ""

#: classicladder_gtk.c:948
msgid "Please select the project to load"
msgstr ""

#: classicladder_gtk.c:956
msgid ""
"Do you really want to load another project ?\n"
"If not saved, all modifications on the current project will be lost  \n"
msgstr ""

#: classicladder_gtk.c:963
msgid "Please select the project to save"
msgstr ""

#: classicladder_gtk.c:969
msgid ""
"Resetting a running program\n"
"can cause unexpected behavior\n"
" Do you really want to reset?"
msgstr ""

#: classicladder_gtk.c:1001
msgid ""
"Released under the terms of the\n"
"GNU Lesser General Public License v3\n"
"\n"
"Written by Marc Le Douarain\n"
"and including contributions made by Chris Morley, Heli Tejedor, Dave Gamble "
"(cJSON), Bernard Chardonneau (base64 transfer) and others\n"
"\n"
"Latest software version available at:\n"
msgstr ""

#: classicladder_gtk.c:1019
msgid "About ClassicLadder"
msgstr ""

#: classicladder_gtk.c:1076
msgid "Save SVG File"
msgstr ""

#: classicladder_gtk.c:1076
msgid "Save PNG File"
msgstr ""

#: classicladder_gtk.c:1148
msgid "Error"
msgstr ""

#: classicladder_gtk.c:1272
msgid ""
"If not saved, all modifications will be lost.\n"
"Do you really want to quit ?\n"
msgstr ""

#: classicladder_gtk.c:1274
msgid ""
"You are currently under edit.\n"
"Do you really want to quit ?\n"
msgstr ""

#: classicladder_gtk.c:1276
msgid ""
"You are currently connected to a target.\n"
"Do you really want to quit ?\n"
msgstr ""

#: classicladder_gtk.c:1318
msgid "Search..."
msgstr ""

#: classicladder_gtk.c:1318
msgid "Not available during edit..."
msgstr ""

#: classicladder_gtk.c:1349
msgid "Variable to search or block number"
msgstr ""

#: classicladder_gtk.c:1459 classicladder_gtk.c:1874
msgid "ClassicLadder Section Display"
msgstr ""

#: classicladder_gtk.c:1482
msgid "Current section selected"
msgstr ""

#: classicladder_gtk.c:1506
msgid "Label of the current selected rung"
msgstr ""

#: classicladder_gtk.c:1515
msgid "Comment of the current selected ladder rung or sequential page"
msgstr ""

#: classicladder_gtk.c:1675
msgid "DEFAULTS"
msgstr ""

#: classicladder_gtk.c:1677
msgid "DEFAULT"
msgstr ""

#: classicladder_gtk.c:1696
msgid "No default."
msgstr ""

#: classicladder_gtk.c:1710
msgid "An error occurred!"
msgstr ""

#: classicladder_gtk.c:1743
msgid "us"
msgstr ""

#: classicladder_gtk.c:1743
msgid "max"
msgstr ""

#: classicladder_gtk.c:1750
msgid "missed"
msgstr ""

#: classicladder_gtk.c:1877
msgid "No project"
msgstr ""

#: classicladder_gtk.c:1881 classicladder_gtk.c:1896
msgid "CONNECTED"
msgstr ""

#: monitor_threads.c:502
msgid "Failed to open this serial port..."
msgstr ""

#: monitor_threads.c:511
msgid "Failed to init and configure modem..."
msgstr ""

#: monitor_threads.c:519
msgid "Failed to call telephone number..."
msgstr ""

#: monitor_threads.c:619
msgid "Too much timeouts errors with remote target..."
msgstr ""

#: monitor_threads.c:672
msgid "Target disconnected."
msgstr ""

#: monitor_threads.c:705
msgid "Target connected"
msgstr ""

#: monitor_protocol.c:996
msgid ""
"Mismatch detected between local parameters and target parameters...\n"
"Perhaps you should disconnect!"
msgstr ""

#: monitor_protocol.c:1282
msgid "Failed to send network config on target!"
msgstr ""

#: monitor_protocol.c:1284 monitor_protocol.c:1389
msgid "Info target"
msgstr ""

#: monitor_protocol.c:1284
msgid "Network config successfully send to target."
msgstr ""

#: monitor_protocol.c:1387
msgid "Failed to send monitor serial config on target!"
msgstr ""

#: monitor_protocol.c:1389
msgid "Monitor serial config successfully send to target."
msgstr ""

#: monitor_transfer.c:175
msgid "Loaded project transferred from target."
msgstr ""

#: monitor_transfer.c:175
msgid "Failed to load project transferred from target..."
msgstr ""

#: monitor_transfer.c:553
msgid "Transfer send completed!"
msgstr ""

#: monitor_transfer.c:601
msgid "Too much transfer errors in response with remote target..."
msgstr ""

#: log_events_gtk.c:99
msgid "Asked to read log events file of the target..."
msgstr ""

#: log_events_gtk.c:128
msgid "***not finished***"
msgstr ""

#: log_events_gtk.c:193
msgid "No file to load..."
msgstr ""

#: log_events_gtk.c:272
msgid "Save CSV File"
msgstr ""

#: log_events_gtk.c:296
msgid "Failed to save the csv log file..."
msgstr ""

#: log_events_gtk.c:317
msgid "Do you really want to delete all events in the log?"
msgstr ""

#: log_events_gtk.c:327
msgid "Id"
msgstr ""

#: log_events_gtk.c:327
msgid "Start Time"
msgstr ""

#: log_events_gtk.c:327
msgid "End Time"
msgstr ""

#: log_events_gtk.c:327
msgid "Value"
msgstr ""

#: log_events_gtk.c:327
msgid "Description"
msgstr ""

#: log_events_gtk.c:327
msgid "Level"
msgstr ""

#: log_events_gtk.c:330
msgid "Events Log"
msgstr ""

#: log_events_gtk.c:371
msgid "Export log to csv"
msgstr ""

#: log_events_gtk.c:374
msgid "Clear All"
msgstr ""

#: log_events_gtk.c:377
msgid "Display only active events"
msgstr ""

#: vars_browser_gtk.c:181
msgid "Vars type selection :"
msgstr ""

#: vars_browser_gtk.c:204
msgid "Bool"
msgstr ""

#: vars_browser_gtk.c:204
msgid "Integer"
msgstr ""

#: vars_browser_gtk.c:205
msgid "R/W"
msgstr ""

#: vars_browser_gtk.c:205
msgid "R"
msgstr ""
