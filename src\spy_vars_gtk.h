
//void RefreshOneBoolVar( int Type, int Num, int Val );
void RefreshAllBoolsVars( );
void UpdateAllLabelsBoolsVars( int OnlyThisColumn );
void OpenSpyBoolVarsWindow( GtkAction * ActionOpen, gboolean OpenIt );
void RememberSpyBoolVarsWindowPrefs( void );
void CloseSpyBoolVarsWindowForEnd( void );
void DisplayFreeVarSpy( );
void UpdateAllLabelsFreeVars( int OnlyThisOne, char * VarName );
void OpenSpyFreeVarsWindow( GtkAction * ActionOpen, gboolean OpenIt );
void RememberSpyFreeVarsWindowPrefs( void );
void CloseSpyFreeVarsWindowForEnd( void );
void ConvertInfoDiskStats( char * BuffConv );
void DisplayTargetInfosVersion( void );
void DisplayProjectProperties( void );
void VarsWindowInitGtk( );
