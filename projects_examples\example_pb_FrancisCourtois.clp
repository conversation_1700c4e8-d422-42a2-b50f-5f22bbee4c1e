_FILES_CLASSICLADDER
_FILE-arithmetic_expressions.csv
#VER=2.0
0000,@200/0@:=@200/0@+1
0001,@200/0@>10
0002,@200/0@:=0
0003,@200/1@:=@200/1@+1
0004,@200/2@:=@200/2@+2
0005,@200/2@-@200/1@>100
0006,@200/1@:=@200/1@+50
0007,@200/1@>1000
0008,@200/1@:=@200/1@/10
0009,@200/2@:=@200/2@/5
0010,@200/1@>=200
0011,@200/1@<=300
0012,@200/3@:=MINI(@200/1@,@200/2@)
0013,@200/4@:=@200/2@|$400
0014,@200/4@&$80=$80
0015,@200/4@&$40=$40
0016,@200/4@&$20=$20
0017,@200/4@&$10=$10
0018,@260/1@:=20
0019,@260/1@:=12
0020,@261/1@>3
0021,@261/1@<=9
0022,@290/0@>=100000
0023,@290/0@<120000
0024,@290/0@>=180000
0025,@290/0@<200000
_/FILE-arithmetic_expressions.csv
_FILE-com_params.txt
MODBUS_ELEMENT_OFFSET=1
MODBUS_MASTER_SERIAL_USE_RTS_TO_SEND=0
MODBUS_MASTER_TIME_INTER_FRAME=100
MODBUS_MASTER_TIME_OUT_RECEIPT=500
MODBUS_MASTER_TIME_AFTER_TRANSMIT=0
MODBUS_DEBUG_LEVEL=3
MODBUS_MAP_TYPE_FOR_READ_INPUTS=50
MODBUS_MAP_TYPE_FOR_READ_COILS=60
MODBUS_MAP_TYPE_FOR_WRITE_COILS=60
MODBUS_MAP_TYPE_FOR_READ_INPUT_REGS=270
MODBUS_MAP_TYPE_FOR_READ_HOLD_REGS=280
MODBUS_MAP_TYPE_FOR_WRITE_HOLD_REGS=280
_/FILE-com_params.txt
_FILE-config_events.csv
#VER=1.0
0,0,4,1,MFAIL,Default test event label...
1,20,1,0,CDONE,Info done counter
_/FILE-config_events.csv
_FILE-counters.csv
14
0
0
0
0
0
0
0
0
0
_/FILE-counters.csv
_FILE-general.txt
PERIODIC_REFRESH=50
SIZE_NBR_RUNGS=100
SIZE_NBR_BITS=500
SIZE_NBR_WORDS=100
SIZE_NBR_TIMERS=10
SIZE_NBR_MONOSTABLES=10
SIZE_NBR_COUNTERS=10
SIZE_NBR_TIMERS_IEC=10
SIZE_NBR_PHYS_INPUTS=50
SIZE_NBR_PHYS_OUTPUTS=50
SIZE_NBR_ARITHM_EXPR=100
SIZE_NBR_SECTIONS=10
SIZE_NBR_SYMBOLS=100
SIZE_NBR_PHYS_WORDS_INPUTS=25
SIZE_NBR_PHYS_WORDS_OUTPUTS=25
MODBUS_MASTER_SERIAL_PORT=
MODBUS_MASTER_SERIAL_SPEED=9600
_/FILE-general.txt
_FILE-ioconf.csv
#VER=1.0
_/FILE-ioconf.csv
_FILE-modbusioconf.csv
#VER=1.0
_/FILE-modbusioconf.csv
_FILE-monostables.csv
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
_/FILE-monostables.csv
_FILE-rung_0.csv
#VER=2.0
#LABEL=
#COMMENT=
#PREVRUNG=10
#NEXTRUNG=-1
1-0-30/5 , 9-0-0/0 , 99-0-0/0 , 10-0-0/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/1
0-0-0/0 , 9-1-0/0 , 99-0-0/0 , 99-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_0.csv
_FILE-rung_10.csv
#VER=2.0
#LABEL=
#COMMENT=
#PREVRUNG=-1
#NEXTRUNG=0
1-0-30/1 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/1
1-0-30/3 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/2
1-0-30/2 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/3
1-0-30/4 , 0-1-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
2-0-30/2 , 2-0-30/3 , 2-0-30/4 , 2-0-30/5 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 51-0-60/4
1-0-30/6 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-60/5
_/FILE-rung_10.csv
_FILE-rung_9.csv
#VER=2.0
#LABEL=
#COMMENT=
#PREVRUNG=-1
#NEXTRUNG=-1
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
1-0-50/0 , 1-0-50/1 , 1-0-50/4 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 9-0-0/0 , 50-0-0/0
0-0-50/1 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0 , 0-0-0/0
_/FILE-rung_9.csv
_FILE-sections.csv
#VER=1.0
#NAME001=PRE
#NAME002=CHART
#NAME003=POST
001,0,-1,9,9,0
002,1,-1,0,0,0
003,0,-1,10,0,0
_/FILE-sections.csv
_FILE-sequential.csv
#VER=1.0
S0,1,0,0,1,1
S1,0,1,0,1,3
S2,0,2,0,1,5
S3,0,3,0,1,7
S4,0,4,0,1,9
S5,0,5,0,1,11
S6,0,6,0,1,13
T0,1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,2
T1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,4
T2,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,6
T3,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,8
T4,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,10
T5,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,12
T6,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,14
C0,0,0/0
C1,0,50/2
C2,0,50/1
C3,0,50/3
C4,0,50/1
C5,0,0/1
C6,0,50/4
_/FILE-sequential.csv
_FILE-symbols.csv
#VER=1.0
%Q1,VA,
_/FILE-symbols.csv
_FILE-timers.csv
2,5
1,5
2,5
1,10
1,10
1,10
1,10
1,10
1,10
1,10
_/FILE-timers.csv
_FILE-timers_iec.csv
0,2,1
1,12,0
1,4,2
2,5,0
1,14,2
1,0,0
1,0,0
1,0,0
1,0,0
1,0,0
_/FILE-timers_iec.csv
_/FILES_CLASSICLADDER
