_FILES_CLASSICLADDER
_FILE-monostables.csv
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
1,10
_/FILE-monostables.csv
_FILE-com_params.txt
MODBUS_ELEMENT_OFFSET=1
MODBUS_MASTER_SERIAL_USE_RTS_TO_SEND=0
MODBUS_MASTER_TIME_INTER_FRAME=100
MODBUS_MASTER_TIME_OUT_RECEIPT=500
MODBUS_MASTER_TIME_AFTER_TRANSMIT=0
MODBUS_DEBUG_LEVEL=3
MODBUS_MAP_TYPE_FOR_READ_INPUTS=50
MODBUS_MAP_TYPE_FOR_READ_COILS=60
MODBUS_MAP_TYPE_FOR_WRITE_COILS=60
MODBUS_MAP_TYPE_FOR_READ_INPUT_REGS=270
MODBUS_MAP_TYPE_FOR_READ_HOLD_REGS=280
MODBUS_MAP_TYPE_FOR_WRITE_HOLD_REGS=280
_/FILE-com_params.txt
_FILE-timers_iec.csv
0,2,1
1,12,0
1,4,2
2,5,0
1,14,2
1,0,0
1,0,0
1,0,0
1,0,0
1,0,0
_/FILE-timers_iec.csv
_FILE-sequential.csv
#VER=1.0
S0,1,0,0,2,1
S1,0,1,0,2,3
S2,0,2,0,2,5
S3,1,3,0,6,1
S4,0,6,0,6,3
S5,0,9,0,6,5
S6,0,4,0,4,3
S7,0,7,0,8,3
S8,0,8,0,10,3
T0,1,6,-1,-1,-1,-1,-1,-1,-1,-1,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,2,2
T1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,2,4
T2,0,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,2,6
T3,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,6,2
T4,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,6,4
T5,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,6,6
T6,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,8,2
T7,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,3,-1,-1,-1,-1,-1,-1,-1,-1,-1,6,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,10,2
T8,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,9,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,8,4
T9,5,-1,-1,-1,-1,-1,-1,-1,-1,-1,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,8,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,10,4
C0,0,0/1
C1,0,0/2
C2,0,0/3
C3,0,0/8
C4,0,0/9
C5,0,0/14
C6,0,0/10
C7,0,0/12
C8,0,0/11
C9,0,0/13
_/FILE-sequential.csv
_FILE-config_events.csv
#VER=1.0
0,0,4,1,MFAIL,Default test event label...
1,20,1,0,CDONE,Info done counter
_/FILE-config_events.csv
_FILE-timers.csv
2,5
1,5
2,5
1,10
1,10
1,10
1,10
1,10
1,10
1,10
_/FILE-timers.csv
_FILE-modbusioconf.csv
#VER=1.0
_/FILE-modbusioconf.csv
_FILE-arithmetic_expressions.csv
#VER=2.0
0000,@200/0@:=@200/0@+1
0001,@200/0@>10
0002,@200/0@:=0
0003,@200/1@:=@200/1@+1
0004,@200/2@:=@200/2@+2
0005,@200/2@-@200/1@>100
0006,@200/1@:=@200/1@+50
0007,@200/1@>1000
0008,@200/1@:=@200/1@/10
0009,@200/2@:=@200/2@/5
0010,@200/1@>=200
0011,@200/1@<=300
0012,@200/3@:=MINI(@200/1@,@200/2@)
0013,@200/4@:=@200/2@|$400
0014,@200/4@&$80=$80
0015,@200/4@&$40=$40
0016,@200/4@&$20=$20
0017,@200/4@&$10=$10
0018,@260/1@:=20
0019,@260/1@:=12
0020,@261/1@>3
0021,@261/1@<=9
0022,@290/0@>=100000
0023,@290/0@<120000
0024,@290/0@>=180000
0025,@290/0@<200000
_/FILE-arithmetic_expressions.csv
_FILE-sections.csv
#VER=1.0
#NAME001=azerty
001,1,-1,0,0,0
_/FILE-sections.csv
_FILE-general.txt
PERIODIC_REFRESH=50
SIZE_NBR_RUNGS=100
SIZE_NBR_BITS=500
SIZE_NBR_WORDS=100
SIZE_NBR_TIMERS=10
SIZE_NBR_MONOSTABLES=10
SIZE_NBR_COUNTERS=10
SIZE_NBR_TIMERS_IEC=10
SIZE_NBR_PHYS_INPUTS=50
SIZE_NBR_PHYS_OUTPUTS=50
SIZE_NBR_ARITHM_EXPR=100
SIZE_NBR_SECTIONS=10
SIZE_NBR_SYMBOLS=100
SIZE_NBR_PHYS_WORDS_INPUTS=25
SIZE_NBR_PHYS_WORDS_OUTPUTS=25
MODBUS_MASTER_SERIAL_PORT=
MODBUS_MASTER_SERIAL_SPEED=9600
_/FILE-general.txt
_FILE-ioconf.csv
#VER=1.0
_/FILE-ioconf.csv
_FILE-symbols.csv
#VER=1.0
_/FILE-symbols.csv
_FILE-counters.csv
14
0
0
0
0
0
0
0
0
0
_/FILE-counters.csv
_/FILES_CLASSICLADDER
