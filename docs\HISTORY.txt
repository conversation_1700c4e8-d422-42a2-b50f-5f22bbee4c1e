CLASSIC LADDER PROJECT
Copyright (C) 2001-2023 <PERSON>
marc . le - douarain /AT\ laposte \DOT/ net
http://www.sourceforge.net/projects/classicladder
http://sites.google.com/site/classicladder
----------
HISTORY...
----------

v0.1 (February 2001 - Not released)
    - The first lines of codes for calculating one rung and displaying it...
    - Basic elements and timers

v0.2 (30 April 2001 - First public release)

v0.2.8 (3 August 2001 - Featuring the Editor, but not totally completed)
    - Basic Editor (allow to modify only the current rung, properties uncompleted)
    - Makefile and modifications to allow independent object compilation.
    - Fixed bug for drawing in calc.c

v0.3 (18 August 2001 - Editor completed)
    - Variables Parser completed for Mxx,R and Txx,D and Txx,R
    - When clicking on "unusable" blocks (part of big element), it is as
      if you've clicked on the "alive" block itself.
    - For timers and monostables , parameters Base and Preset.
    - Can modify label of the rung to jump to for -(J) coil.
    - Label / comment of rung saved when edited.
    - We must not allow to change of current rung when editing one.
    - Add/Insert/Delete a rung, buttons usuable.
    - Load/Save/SaveAs/Run-Stop/About buttons added in main window
    - Load/SaveAs using path selection requester (datas should be in a directory).
    - Save rungs,timers,monostables parameters.

v0.4 (20 October 2001 - Arithmetic expressions evaluator)
    - New variables memory type : Words (integers)
    - 2 new elements using arithmetic expressions :
      COMPARE and OPERATE
    - Load/Save the file of arithmetic expressions
    - Possibility to spy the values of any variables by typing its name
      (added in the bottom of the vars window)
    - Added new types of variables : physical inputs (I) / outputs (Q)
      (not linked to anything in this version)
    - Resizing of rung take into account and thick lines for active wires
      (modifs initially done by Jiri Baum when integrating classicladder
      into the MAT project)

v0.5 (30 December 2001 - Real time support)
    - ClassicLadder_gtk.c created from file classic_ladder.c which keep only the
      global variables + 2 fonctions for loading/saving directory datas.
    - Global variables are now allocated and freed.
    - RTLinux support added.
      The refresh of the rungs are done in a RTLinux module in real-time.
      The Gtk application compiled for this version do not refreh the rungs.
      The Gtk application and RTLinux module share the same global variables
      allocated with mbuff.
    - Physical inputs and outputs have the same memory as the bits (VarBits + offsets).
      (if we make polling on them in RTLinux module with inb() and outb(),
      we could drive real hardware directly !?)

v0.5.1 (4 January 2002 - Parallels ports interface)
    - Direct hardware support of the parallels ports (in real-time version).
      Comment the line define HARDWARE_SUPPORT in classicladder.h if you do not want it !
    - Bug if there was no element in the first column, fixed (in function StateOnLeft())
    - Overflow possible (1 char) for label and comment strings in the rung, fixed.

v0.5.2 (26 January 2002)
    - Added in the editor the nice long connection (-->) to create rungs faster.
    - In Operate elements, you should now write the formulas like the following:
      A:=B instead of A=B
    - Draw a grid in edit mode.
    - #include <rtl.h> removed in hardware.h as it seems not to work with kernel 2.2
      causing an error when compiling : duplicate module kernel version...
    - Removed the warnings when compiling for RTLinux.
    - For big element, at the point you click, it is the left-top corner of the element
      which is inserted instead of right-top (the "alive" one).
    - Confirmation box before deleting the current rung (indispensable!)
    - 'New' button with a confirmation box also ;-)
    - Bug (exception) possible, if replacing for example a contact with a timer, where
      the number of the bit in the contact was greater than the timer number allowed.
      Now the number is erased if a simple element is not replaced by a simple
      element, to avoid that.
    - When deleting the current rung, the arithmetic expressions used on that rung were
      not deleted. Fixed.

v0.5.3 (11 May 2002)
    - Taken the current sources of classicladder from the MAT tree.
      So that we will have the same sources on the two sides...
      There is a define in the Makefile MAT_CONNECTION to be able to
      create the mat connected version for the MAT project.
    - The real-time support was broken in the MAT version.
      Modified the way to alloc/free booleans variables so that it works back.
      Now it should works for the 3 possibles versions : normal, real-time and mat.
      Note: it is not possible to include "mbuff.h" in many sources files : otherwise
      we have multiple definition when linking of some functions...
    - Modified Makefile for easier real-time generation.
      make rtl_support ; make test is enough for the rtlinux version
    - The feature that everybody was waiting for : added a vertical scrollbar to
      show many rungs at the same time...
      The current rung is the one with the blue bars.
      The current rung is set automatically for the moment : it is the rung which is at
      the top and that is fully visible...
      There are still some visual troubles when adding/inserting a rung, but not really
      serious... to be modified in a future version.

v0.5.4 (22 May 2002)
    - Sometimes, at the end, the last rung was displayed 2 times : for example if
      there was only one rung existing and you increase the vertical display...
    - During edit mode, if you clicked outside of the current rung (top or bottom),
      it was accepted and making strange things.
    - Now when you insert or add a rung, you have a correct display of the rung before
      or after the new rung you are adding.
    - Problem when adding or inserting if the current rung is the first one. Fixed.
    - If the vertical size was less than enough to see a complete rung, there was no
      more current rung. Fixed.
    - Do not allow to set 'connection with top' if on the first line of the rung.
      As we have now many rungs, it was as if 2 rungs could be linked ! ;-)

v0.5.5 (18 August 2002)
    - Added And, Or and Xor operators in the evaluator : & , |, ^
    - Added hexadecimal constants support in the evaluator with the first carac : $
    - Display formats : Dec/Hex/Bin for vars spy.
    - Bug-fixed if compiled for Windows : '\' used instead of '/' for directories.
    - Show the duration of the last scan of the rungs in micro-seconds if calc of the
      rungs is done under RTLinux.
    - Program can be split in many sections : new window 'Sections manager' added.
    - New format of the rungs files including Prev & Next rung informations. When saved,
      the rung number do no more change.
      Warning, old classicladder versions will not be able to read these new files !
    - New (C)all coil for sub-routines.
    - Modified RefreshAllRungs( ) for taking into account the main sections and the
      calls to sub-routines.

v0.6.0 (2 November 2002 - Birth of a new language)
    - Added a new language : "Sequential" (sometimes also called Grafcet).
    - New variables supported : Xxx and Xxx,V for the steps of the sequential.
      Xxx is activity of step xx.
      Xxx,V is activity time of step xx.

v0.6.1 (23 November 2002)
    - The RTLinux version support was completely broken (perhaps since v0.5.5).
      Now it works correctly as before.
      Tested here with a fresh installed RTLinux3.2pre1 with kernel 2.4.18.

v0.6.2 (23 March 2003)
    - When launching classicladder, you can give now an argument with the path
      of the files to load.
    - Horizontal & vertical scrollbars when displaying sequential added.
    - Xxxx and Xxxx,V variables now use the step number defined for each step
      instead of the number in the array.

v0.6.3 (15 April 2003)
    - Embedded version possible without the Gtk interface
      (comment GTK_INTERFACE in the Makefile).

v0.6.4 (19 July 2003)
    - Possibility to load a config file at startup with the sizes of differents
      arrays to alloc. Interesting for really embedded system where there isn't a
      lot of memory. Patch send by Thomas Gleixner.
    - Some big variables only usefull for editor no more allocated if compiled for an
      embedded target without GTK. Patch send by Thomas Gleixner.
    - Possibility to build a library with ClassicLadder. Patch send by Thomas Gleixner.
    - Parameters added to the RTLinux module to be able as in the normal version
      to use the sizes choosen.
    - Made a script (run_rt) to be able to launch the real-time version without having
      to use the Makefile for that, and made the necessary to pass the sizes parameters
      to the RT module.
    - Add a config window with the sizes actually allocated and the percent of use for
      some of them.
    - When a ladder section was deleted, the rungs associed to it were not freed.
    - In FindFreeRung( ) start to search from rung 0 instead of the FirstRung value !?

v0.6.5 (17 August 2003)
    - Physical inputs/outputs interface completly rewritten :
      Removed the parallel ports access code.
      Added possibility to map the logicals I/O of classicladder with any addresses ports
      or a Comedi device (www.comedi.org). Thanks to Edouard Tisserand for that nice
      idea and for the 'LinuxInControl' workshop at Leuven to let me discover
      that project.
      Parameters can be easily edited in the new tabs "Inputs" and "Outputs" in
      the configuration window.
      New file : ioconf.csv in the projects directories.
    - Calling ioperm( ) to allow to read/write adresses ports directly
      even if not under RTLinux ! (but you must be root, so that it works...)
      A lot of people wanted it : no real-time, but hardware access under Linux, which
      is enough and easier in schools to learn ladder programming!
    - In the RTLinux module, added binary parameter "COMEDI_TO_OPEN_MASK" which
      is used in init_module( ) to open the Comedi devices necessary. When the
      module is insmoded, the parameters files are not already loaded, and we
      can't know which Comedi devices will be used later.
    - ./run_rt PROJECT_DIRECTORY CONFIG_PLC_FILE to launch real-time version.
      The arguments must be given in that order.
      The configplc file contains the parameters used in the init_module( )
      when the RT module is insmoded (memory sizes to alloc and Comedi devices
      to open).

v0.6.6 (10 November 2003)
   - RTAI support. Patch send by Paul Corner. Thanks to him for that nice work.
   - Some adjustements so that RTLinux works again... I hope I have not broken
     something for RTAI. I've not take the time to install it for now, so
     can not say, if all is still working correctly...
   - File config_gtk.c failed to compile with old gcc 2.95, because of some
     variables declarations in switch after case. Fixed by adding { } blocks.
   - File files_sequential.h, strange warnings on gcc 2.95 and my Pegasos with
     PowerPC processor with test NumPage!=-1 (said comparison always true due
     to limited range of data type). Added (char) before -1.
   - Added a new define in Makefile (MAKE_IO_ACCESS) so that it works on some
     processors others than x86. For example, PowerPC !
   - arrays.c bad test to verify alloc successfull for sequential array (Paul Corner).
   - Script run_rt was not working if you don't give any configplc file as 2nd
     argument (RTLinux modules were not inserted).

v0.7.0 (3 January 2004 - Sequential Editor added)
   - Added the sequential editor !
     It's the first version of a big part of code, so do not be too much suprised
     if somes cases do not work correctly as they should! ;-)
     I've made some tests on little examples, and it is already possible to make
     some nice grafcets easily !!!
     With the editor, it will also be possible to test the calc part with another
     grafcet than the single example given since November 2002...

v0.7.1 (13 March 2004)
   - Thick lines for drawing steps and transitions activated.
   - edit_sequential.c failed to compile with old gcc2.95. Fixed.
   - Grid displayed during edit for sequential pages.
   - In I/O config do not display /dev/comedi* if not compiled for.
     + call ConfigHardware when window closed (Jiri Baum report).
   - Properties window is visible only during edit and can not be closed.
   - Vars and Edit Toolbar windows can not be closed.
   - In toolbar, per default the pointer is directly selected.
   - No more GTK warning it writing a boolean var >=40 (no checkboxes).
   - Confirmation requester before quitting application.
   - When delete all with "New", manager window content was not refreshed.

v0.7.2 (21 August 2004)
   - Auto-numbering of the steps and transitions when created if there is one found
     directly above it. At least, always set a free number step per default. 
   - Properties window: if you type return at the end of the strings entered, it is
     no more necessary to click on "Validate" with the mouse.
   - Projects are now composed of an unique file, which contains all the little
     parameters files. A tmp directory is used to store the little files that we
     split/join. Old projects in a directory can still be loaded.
   - Arithmetic expressions: negatives constants support added, '!' for negative term,
     functions: MINI(), MAXI(), MOY() and ABS().
   - Modified vars window: now display %B, %I and %Q with an offset for each to start.
   - The step number written above the cross step was not correct (in example, written
     step "0" above step "0" instead of step "7": from where we come).

v0.7.3 (28 December 2004)
   - Modifs done for Win32 port included (end of line and '/' in files for project, and
     some variables declarations in the middle of code not supported with gcc2.95).
   - Using "/tmp" instead of "~" for the temp directory if no TMP env variable found.
     ("~" was not working on some distributions like Debian Woody).
   - InitTimers and InitMonostables at the start added. Else it was crashing if saving
     project from new (reported by J-Ch Dorian).
   - Directly edit of properties with keyboard possible (idea by J-Ch Dorian).
     (window is closed then opened so that it is on top actived, GTK1.2 miss a present()
     function for windows...?)
   - Modbus/TCP server added (default port is 9502).

v0.7.4 (15 August 2005)
   - Modif config_gtk.c (deleted ConfigHardware) for RT_SUPPORT.
   - Modbus master protocol added for distributed I/O.
     Both IP mode and serial link (Modbus/TCP and Modbus/RTU).
     Gtk config window for it.
   - In the 6 bytes IP specific header of Modbus/TCP frames in socket server, the number
     of bytes emissed was missing 1 byte.
   - Bug when adding "OPERATE" by clicking on rung in the two last columns. Corrected.

v0.7.5 (13 May 2006)
   - Added "reset" button in the gtk interface.
   - Using single temp directory (allowing many users launching each one a classicladder
     without conflict).

v0.7.60 (27 May 2006)
   - Switched to GTK2 (thanks to Miguel Angel for the initial patches).
   - Using the new GTK2 filechooser (again Miguel Angel here).
   - Could freeze if only one ladder displayed, and height of the section window increased.
   - Centering strings displayed everywhere if GTK2 used in the section window.
   - Properties window displayed in front better than with old GTK1.2 (new function
     gtk_window_present() available).
   - Added filter with *.clp (and *.csv for very old projects) in the filechooser.
   - Bad design containers for the free vars widgets, awfull with GTK2. Fixed.

v0.7.70 (11 June 2006)
   - Display preset values of timers/monostables when editing instead of nothing.
   - A print button is appeared...
     Actually displaying a preview window from which you can print.
     No printer on the computer I used, so absolutely not tested!
     If you set other paper than default A4 in portrait , it should not work...
     First time I use gnome_print so I've not all understood (and poor doc does not help),
     and quite happy to have done that in one afternoon/evening for now ! ;-)
   - And a new edit button (now the toolbar editor window can be closed and then reopened).
   - The Makefile can set destinations directories (patch from Paul Schulz).

v0.7.71 (20 June 2006)
   - "Print" button becomes "preview" one, and added a print function to allow you
     to select the printer you want (I've used it to be able to print in a pdf file!)

v0.7.80 (18 July 2006)
   - Added the new following variables Tx,V & Tx,P & Mx,V & Mx,P
     (on the TODO list since nearly the start, but claimed by Jarl Stefansson, and so here it is!)
   - Lot of modifications done in the generic variables parser to be able to use theses new
     variables in the arithmetic evaluator.
   - Added verification that the type of variable is a boolean for contacts/coils.
   - Rename buttons "Validate" per "Apply" in properties and "Ok" in toolbar.
     These are modifications that where done in EMC2 version (I've recently discovered), integrated back.
     Undoubtedly, others could be interesting, but it's a hard work to see everywhere ! ;-)
     Lot of modifications for Kompare, in fact aren't... (too much "indent" work !?)
   - The temp directory is no more created in the classicladder directory. Modified again
     (last time it was in v0.7.5)
   - New function block family available: counters one.
   - And the new variables for them: Cx,D Cx,E Cx,F Cx,V Cx,P

v0.7.90 (11 August 2006)
   - Fixed outputs of the new counters blocks, not working if used directly in a rung...
   - Totally rewritten the old toolbar done by hand at the time. Now using radio buttons
     with customs images. Really nice, especially compared to the old one!
     The toolbar editor window is not opened per default now.
   - A new element "comment" available in the sequential ("[xx]" in toolbar), taking 4
     horizontals blocks.
   - GTK properties window can be sized to see entirely long strings (expressions & comments) !

v0.7.91 (17 August 2006)
   - Saving current position of the properties window when hiding/showing it.
   - Clicking on a rung (before being in modification) in the section display (with the window
     increased vertically displaying many rungs) allow now you to select the "current" rung,
     the one that will be modified when edited.
   - Possible partial print of the grafcet (if the lifts not set at the left-top). Fixed.
   - Modified color for the corners of comments in sequential (usefull to read long strings).
   - Confirmation quit message now displayed only if parameters modified (but no deep tests
     to verify that it isn't the same parameters than before!)
   - To delete a "big" element, you had to click on the top-right of it. No more necessary.
   - Bug in variables parser no more accepting any Wxxx variables in expressions... Fixed.
     And added many new error messages to explain why a variable has been rejected.

v0.7.92 (24 September 2006)
   - Xenomai (real-time extension) support added (in user-space).
     Uncomment the corresponding line in the Makefile, and compile with "make clean;make".
     Then launch with the normal "./classicladder" !
   - a new pthread (cyclic calc) is used for both Xenomai and normal version (instead
     of using the gtk timer, only used for gui refresh now).

v0.7.93 (21 October 2006)
   - Hide print/preview widgets buttons if compiled without gnome-print (patch by Marc Ferraton).
   - Segmentation fault with "Linux version" possible (after few hours of running)
     caused by the gtk function called from the calc refresh thread (in WriteVar) without
     anything to protect.
     (replaced by a request to refresh all booleans vars displayed in the gtk timer, like with
     all real-time versions: RTLinux, RTAI, Xenomai).
   - Cyclic calc refresh not stopped (when in stop), regression with the new thread of v0.7.92
   - Corrected Makefile for embedded version (GTK_INTERFACE in comment) with normal Linux
     or Xenomai.

v0.7.100 (4 November 2006)
   - List of symbols available for each variable (partials or completes symbols) used in
     display and when entering a variable name.
   - Checkbox widget added in section display, to switch display between variables names and symbols.
   - New Window to edit the symbols list.
   - Renamed variables always starting with '%' character and attribute separator ',' replaced
     by '.' (IEC61131 form).
   - Size of rung blocks modified: more large (to display symbols and variables names larger
     because of the % added) and no more squared (to keep many rungs visible vertically!).

v0.7.101 (18 April 2007)
   - Now calling ConfigHardware from the cycling thread of calculations, because the ioperm()
     seems to have be called from there (regression from the introduction of the thread
     instead of the gtk timer).
   - Applied some warnings patches and for 64 bits architecture tests from Paul_C (thanks to him!)
   - Some EMC2 modifications done on signed char not assumed for "NumPage" sequential variables
     + re-order declarations to support old compilers such as gcc 2.95 (people still using it?!)
     + GTK_RESPONSE_ACCEPT and GTK_RESPONSE_OK constants in load/save projects requesters
   - Tooltips added in the vars window for the three top entries.

v0.7.110 (8 May 2007)
   - Modified copy of the strings in symbols window and displayed truncated immediately when
     entered.
   - If classicladder launched with project parameter parallel_port_direct (in line command
     parameter), was crashing (like in v0.7.101, where ConfigHardware must be called in the
     logic calc thread).
   - Reset button now init all the variables.
   - When another section choosed (and under edit), current operation wasn't really properly
     canceled (discovered with the fact that the positions of properties window were lost).
   - Filter on projects names *.clp in load/save dialog (and for old csv directories).
   - Show symbols names (if checked) instead of %Tx, %Mx, %Cx for blocks.
   - Little modification in sequential edit (CommonSearchForManyStepsOrTransi) from emc2.
   - Contant ARITHM_EXPR_SIZE more longer, otherwise too short with some variables...
   - Display error messages gtk requester if hardware config failed (just a message in
     current console before...)
   - If an eval/compar was set with a blank arithmetic expression, its same buffer was then
     used again with another one (now '#' reserved string).
   - Created new file spy_vars_gtk.c (extract vars window part from classicladder_gtk.c)
   - Spy var window can be closed now.
   - Added "Vars" button in section window to open spy vars window if closed.
   - "nogui" parameter support (inspired from emc2 work, but rearranged with better
     split between gtk parts and not). Now, ctrl-c is also trapped if gtk started.
   - Toggle show/hide vars/symbol/editor window when clicking on buttons (nice idea by
     Chris Morley, but using the GTK_WIDGET_VISIBLE() macro more simple for code, isn't it?)
   - More tooltips on the toolbar (again thanks to Chris Morley for idea and patch, but
     all strings helps stored in two arrays for ladder & sequential).
   - Period refresh value can be adjusted in the config window and is saved in the project file.
   - Can now adjust the sizes contants in config window, values saved in the project file.
   - Some reorganisations and clean-up of code in general...
     (erased DYNAMIC_PLCSIZE constant, always used!)
   - Example parallel_port_direct had a blank I/O configuration since a long time...

v0.7.120 (13 june 2007)
   - Added tooltips on label/comment entries of the rung selected.
   - New timers with 'TON'/'TOF'/'TP' modes as defined in IEC61131  (keep monostables and
     timers for projects compatibility but now called "old"!).
   - Variables names strings in a new file "vars_names.c".
   - Symbols names support in arythmetic expressions.
   - On old timers, added a new 'C'ontrol input used to freeze the compter if not enabled.
     (you will have to modify your old projects to link the "E"nable and "C"ontrol to have
     the same result than before).
   - "AVG" support for "MOY" function (the french word "MOYENNE" for average).
   - When a 'J'ump coil is activated, abort immediately the refresh of the current rung.
   - A bug with a very simple sub-routine section call example reported by Chris Morley,
     decided me to reorganize all the rungs sections principle. Now using a function
     called recursively. Really more simple than before, and so without bug, well I hope!
     + had infinite loop jump test used to stop all!
   - Before you should have only one (C) coil in each rung, else the others top (C) coils
     were not taken into account.
   - No error were given for the following case expression: MOY(constant_value,variable),
     but MOY function only support variables for calc result! (seen by Chris Morley).
   - In spy free vars window, add corresponding symbol/default var name  with tooltips.

v0.7.121 (8 July 2007)
   - "*** stack smashing detected ***" error message when loading symbols bug under
     KUbuntu 7.04 I freshly installed. Strange bug... Added some end line securities.
   - Confirmation requester before deleting a section added!
   - When putting a new function block, per default use one not used (for default number).

v0.7.122 (26 August 2007)
   - Variables indexed support added in expressions.
     Example: %W0[%W4] => if %W4 equals 23 it corresponds to %W23 !
     See the new example used for tests "VarIndexed_used_in_function.clp".

v0.7.123 (4 November 2007)
   - Define added to be able to compile (or not) the old timers/monostables blocks.
     (OLD_TIMERS_MONOS in Makefile) 
     Usefull for compatibility with old projects. The new IEC timers can do all !
   - Backported the Windows modifications done for the port.
   - When clicking on a ladder element, display its properties in the new status bar.
   - Modified color of the grid during edit.
   - files.c splitted (new files_project.c for project part).
   - Now saving only not blank arithmetic expressions (number added before) in file.
   - Some preparation works in files.c (xml comment format line+"cl_" prefix for file names).
   - Again some cleanups...
   - Default font changed for a smallest: "Courier New 8". Perhaps another one could be better...
   - Default variables names now defined in a table (instead of directly in the parsing/
     displaying text functions).
     Lot of code changes behind, hope nothing broken...
   - In internal TimerIEC have preset and current values now in number of base units, and
     using WriteVar() call to updates them (just usefull for me on another project).
   - In internal Counters using WriteVar() call to updates public variables (same reason).

v0.7.124 (2 January 2008)
   - Removed some Valgrind "Invalid read of size 4" in CyclicCalcRefresh() on exit.
   - Merged back some fixes done in calc for the another project Dragon (important error
     with the 0.7.123 WriteVar() internal modifications!), and some others things...
   - Some fixes in the text parser for arithmetic expressions (all variable names must
     had a number after first 2/3 characters + possible trouble if % used as operator).
   - New define MODBUS_IO_MASTER to add possibility to exclude properly this part.
   - A new interesting example included "3pumps"...
   - Fixed following old bug: 2 sections defined in project, delete first (in internal
     number 0), then reload project, first one still defined (the default section "Prog1" !)...
   - During edit, see the current selected element with a red rectangle around it.
   - When erasing, was putting an inexistant "erase" element instead of correct free one...
     (some existent rungs projects can have that!) Drawing theses abnormal elements "(xx)"
     to see them! (Now understand the case with long connections not working!!! because not "free")
   - Confirmation requester if loading selected, and current project modified and not saved.

v0.7.125 (18 February 2008)
   - Added in variables array description, if "read only" or "read/write", and use/verify it
     to edit (read/write required for coils + read/write target variable in operate block).
   - Possibility to modify current value of a "read/write" variable (in a new window).
   - When editing expression (compare/operate block), if incorrect, keep the previous expression
     instead of setting it to "#" !
   - Added a "Properties" button in section window to be able to modify its name.
   - To be able to modify on run/fly a %Cx.V variable (now in read/write access).

v0.7.126 (23 February 2008)
   - Added some securities during arithm eval to avoid crashing (floating point exception).
     Example strings: "%W<4" "%W0:=123++" "%W0:=123%%"... should trap thoses errors now... 
     Bug reported by Chris Morley (EMC2 project).
   - Bad (same) "Add a section..." title for properties section window. Little bug fixed.
   - During rung edit, possibility to had 2 big elements overlapped (outside alive cell of
     the element added).
   - Confirmation quit requester if under edit.

v0.7.127 (22 April 2008)
   - "Abnormal current type found" warning message when deleting. Fixed.
   - Problem to delete a symbol defined for a variable (reported by Chris Morley).
     Fixed in the gtk edit symbols window (possible to delete the variable).
   - Indexed variables are now available not only in arithmetic expressions, but also in all the
     elements of the rungs !
   - Current project file name displayed in section window title.

v0.8.0 (7 August 2008) [Big switch to Cairo & gtk-print]
   - New constant type support: character like that 'E', in arithmetic expression.
   - Some little Dragon modifs backport (missing AskConfirmationToQuit set, exit return code usage,
     display both current value & preset in functions blocks)
   - Started a little some backports from current EMC project version done by Chris Morley.
   - Removed old GTK1 codes parts (to clean up, and no more tested and ugly for texts...)
   - Totally switched the drawing from gdk calls to Cairo graphic library. It is now used to
     print with gtk-print instead of the old gnome-print, and the vector rendering is really
     better than the old bitmaps done ! Moreover it should works under Windows...
     A lot of works done... perhaps still some little things to improve.
   - Now current selected element during edit drawned filled with alpha.
   - See better the selected rung with a light yellow background + added error message if clicked
     outside of it during edit.
   - Header label/comment now drawed at the top of each rung (and used to print, done for it
     in fact, but it's better no?!).

v0.8.1 (23 August 2008) [gtk-print also working under Windows... finally not so simple]
   - Many, many modifications done on the graphics rendering to remove all the many fixed in hard
     pixels constants for some little values (the printing cairo context under Windows gives
     a width/height size largely bigger than under Linux, explaining why I don't see that problem
     before...). Font sizes also have to be different...
     And many cleanups in general on the offsets for the ladder rungs... it is visible!
   - Left/right bars for rungs present when printing.
   - No more 'Print preview' button since switch from gnome-print (Linux only) to gtk-print now
     available again (it is so easy that I don't find how to do it at the start with gtkprint:
     just another action constant for gtk_print_operation_run(): 
     GTK_PRINT_OPERATION_ACTION_PREVIEW instead of GTK_PRINT_OPERATION_ACTION_PRINT_DIALOG).
     Nice simple example with the gnome-dictionary software source code!
     You must have the evince gtk package installed.
   - Strange bug (since when?) on top buttons in toolbar depending of the language, if clicking
     on another section in the window manager (I'm sure it was working before...!)
   - SVG export of the current rung or sequential page (an easy Cairo feature here...)
   - During edit on a free place, set default variable type (input or output) depending
     if contact or coil (EMC, Chris Morley modification).
   - New "words" variables for physical inputs/outputs %IWxxxx & %QWxxxx (used for distributed I/O
     modbus master).
   - Support of read/write registers code functions (4, 6 & 16) added in Modbus distributed
     I/O (Chris Morley work, but a little revamped by me). Not tested for now by myself. Should works
     if I didn't do a mistake when copying and modifying the code...
   - Renamed variables %Xxxx in %Xxxx.A (trouble with the parser, easier like that!)
About gtk-print (under Windows only): with the preview (using the windows preview utility) and with
one printer (on two used!) I've obtained some black boxes during my tests... not understand why for now?
Else the preview on Windows doesn't works if more than one page rendered (all on the same one), perfect
on Linux side.

v0.8.2 (7 September 2008) [configplc startup file removed]
   - Removed completely the configplc file no more necessary with a new file "com_params.txt"
     in the projects for the serial times parameters, and Serial port name & speed in the
     file general_parameters (loaded before initializing all).
     + New "Modbus com settings" page and some modbus distributed parameters moved there.
   - I/O Modbus Master: added function read coils + read hold registers (from EMC).
     + added function read status "7" (response value stored in the %QWx choosed).
     + added function diagnostic "8", use field '1st modbus element' to store the sub-function
     number to send, send the data from %IWxxxx and store the data received in the %QWxxxx (using
     the same xxxxx mapping variable choosed).
     NOTHING TESTED FOR NOW...
   - Right bar drawed a little too much on left. Fixed.

v0.8.3 (13 December 2008)
   - During edit, a little element can no more replace a big one (else trouble with blocks
     unavailables remaining).
   - During edit, disable the widgets in the sections manager to avoid possible troubles...
     (and as it is not the time to play with that...!)
   - Added unit defined on a char in each timer block after current and preset values.
   - Maxi preset for timers now fixed to 9999.
   - In properties windows, for timers set focus to the preset edit line per default
     (instead of the number, usually never modified).
   - When deleting an element, try to delete also smartly the verticals bars... and if no
     element will delete a vertical bar present.
   - When opening the edit and properties window, move them near the main section window.
   - When printing, add in the right margin for each rung the contents of the compare/operate that
     where not displayed entirely (with a report number) + when displaying, better display of the
     string at the max place and "(..)" after COMPARE/OPERATE if not displayed entirely.
   - Added a pixel height to the vertical drawing.
   - Bug report by Chris Morley in build of the config window for the modbus I/O slaves.
   - Added a validity test in ABS() function with only one variable (Chris Morley report on EMC).
   - Right bar printed a little too much on left. After the bug-fix to draw! ;-) Fixed.
   - The same bug in the svg export. Fixed.
   - Switched to LGPL version 3.

v0.8.4 (26 April 2009) [Rung part Copy feature]
   - Moved internal variable CurrentProjectFileName into struct InfosGene (EMC rework, why not!)
   - Some fixes in modbus master with some 8 bits conversions (taken from EMC).
   - Display current full path/file project in config window.
   - Radio buttons (instead of simples entries) in modbus com parameters (idea from EMC but
     compactly rewritten, hello Chris! what do you think of it? ;-) ).
   - Can map any types of modbus request to any types of variables (an EMC feature idea).
   - Ask confirmation before reset (from EMC).
   - Ghost zone rectangle for the element selected in the toolbar (for current position of the mouse) to see
     exactly the blocks place it will take if set there, and where it can be put (rules respect).
   - Long awaited feature: part selection of a rung that can be then copied somewhere ! Two new buttons
     in the toolbar available for that (selection & copy).
   - For Sequential, do not allow step on last line, reserved only for link usage... (Francis Courtois report).
     A lot of things still have to be done on the sequential part... the poor part, all major dev is done on
     rungs ladder!
   - Double licence : LGPLv3 & LGPLv2 for EMC usage.

v0.8.5 (5 July 2009)
   - New system variables %Sxxx and %SWxxx : %S0 is the classic 1Hz flash, always usefull...
     And %SW0, %SW1 & %SW2 for BCD time, date & day of week.
     %S10 is set per the modbus master if error of communication (EMC principle), in the future I've planned
     to log any variables wanted and have a list of the current defaults events, you see where I want to go!

v0.8.6 (25 August 2009)
   - Events log of variables parametered (in a new config tab) which state changed (with log time display of start/end)
     in a new window "Log"
     + display new 'defaults' (which have their DataTag>0) in the status bar.
   - Reorganised Modbus with a new defines file used for both slave and master
     + in slave, abstraction of the vars.
   - In modbus master, in rtu could crash if frame received with less then 4 chars... Seen on my little avr module test.

v0.8.7 (6 April 2010)
   - Pauses in ms not working if >= 1 second ( in DoPauseMilliSecs() ).
   - Inter-pause time in modbus can be set to 0... tests on my avr i/o module!
   - Added in hard a 10 ms pause before RTS off.
   - Was crashing when started with the default example (logging %B0->%B3 and %B20 var demo!), doing "new", and then
     setting in boolean var, one of them... mad loop... fixed!
   - Added PNG export of the current rung or sequential page (So easy with Cairo...)
   - Added copy to clipboard function... not so easy to find how to do it, used a .png temp file.
   - Fixed trouble on step n°0 vars (X0.A & X0.V) conflict with steps not defined...
   - Like in rungs, now current sequential selected element during edit drawned filled with alpha.
   - Sequential, added some tests errors and help messages, cross step numbers colored when transition activated, cleanup link
     transition<->step deleted, ...

v0.8.8 (19 May 2010)
   - Serial low-level functions added in Windows (used for Modbus). Tested with my avr modbus module (in RS485) with real
     serial com port with an RS485 converter on it (requiring RTS command to send) + USB-RS485 converter.
   - Modifying modbus serial port do no more need to save & restart software with this parameters
     + if failed to open port: error in a message window  + no misleading modbus poll running!
   - Better serial modbus frame testing : search first char with address (permitting to ignore extra chars before if any)
     + ignoring extra chars at the end (if any).

v0.8.9 (27 November 2010)
   - Added serial init extra parameters: nbr.bits/parity/nbr.stops (EMC modifications)
     + theses parameters in the Gtk options window.
   - Renamed CreateEvent() to CreateEventLog() in log.c and log.h (error with #include <windows.h> and #include
     <shellapi.h>), Heli Tejedor modification report.
   - No more need to click on element to display its properties in the new status bar. Mouse move on it enough -
     Heli Tejedor modification.
   - Displaying in status bar both symbols names if available and real vars names between parenthesis (for variable
     indexed or not, arithmetic expressions) - Nice interesting idea of Heli Tejedor.
   - Alert error if too much expressions used (during adding evaluate/operate blocks) instead of using the first
     already used, and no more creating bad function block number 0 to manually delete if no more avalaible!
   - Add GTK+ version information in the about window, and started to make it in a classic about dialog.

v0.8.10 (31 December 2010)
   - In Modbus I/O module debug Tx, display address + function code explicitely.
   - GUI Menu/toolbar revamp. Many thanks to Heli Tejedor to show me how ClassicLadder could be nicely transformed by
     adding a menu and a toolbar instead of my old fashioned buttons (same since the start of the project...)
   - Bug in gtk modbus options config on closing (for example modbus outputs not refreshed correctly if debug not on
     only "level 1" !) Since new serial parameters added in 0.8.9...
   - Properties window, to disable "apply", set sensitive true/false instead of invisible (look better, by avoiding
     dynamical resizing of the widgets).
   - Modbus master was not working if project not given in parameter, but loaded after (worked only after opening/closing
     the config window). Working now (but fast fix perhaps to look again later...)

v0.9.0 (23 April 2011) - New "monitor" protocol target added (IP UDP) & more elements per default (and bigger rungs) !
   - Added symbol bubble help on checkboxes in 'Spy bools' window.
   - Save in the project, latests free vars selected + latests offsets for bools ones.
   - New monitor protocol added to be able to connect to a target in IP (UDP)
     (to read free vars, bool vars, date/time, run/stop state, read rungs activity, read sequential page activity).
     See menu "PLC"/"Connect".
   - Optimised saved files for functions blocks (only ones saved with number at start of line)
   - More all things per default (functions blocks, rungs, ...)
   - Bigger ladder rungs in x & y !
   - Renamed extension ".clprj" to avoid ".clp" corresponding to Windows clipboard files... no luck when choosed, under
     Linux at the start of the project ! Format changed (see 2 previous lines...)
   - Fixed month in system var %SW1 (starts at 1 for january, instead of 0...)
   - Display current date/time at the top of free vars window.

v0.9.1 (25 June 2011)
   - In free spy vars, replaced Old Combo per ComboBox (to avoid warning message because old deprecated widgets?...
     and best look with only one of the items selectable).
   - Possible trouble in files when loading modes timers with line "#" read as a timer (then segmentation fault when
     displaying anywhere the timer mode...) and no preset set when project saved... yes we can have the case! I had...
   - Recast modbus part with slave infos splitted from request. easier for IP communication part, and that allows to
     have an error system var per slave (%S10 to %S19) + statistics infos for each slave. 10 slaves possible.
     + better errors recovery in case of IP.
     File project compatibility "broken" (for I/O modbus), you will have to set back the address of the slaves !

v0.9.2 (21 August 2011)
   - As in free var spys (done in 0.9.1), replaced Old Combo per ComboBox in sections manager window.
     Also in properties window for timer base and mode.
     Also in config window for physical inputs/outputs.
   - When monitored, bad display of inputs&outputs pins of big block elements... it was hard to debug: needed to modify
     calc (a little) & draw (a lot) for them...
   - Remember latest IP/hostname entered for monitoring.
   - Run/Stop + Reset of the target possible (monitor protocol).
   - Set clock time of the remote target (using current local PC clock time)
   - Starting per default without project and stopped, no more "example.clprj" loaded per default, and ... see below !
   - New preferences file (".classicladder_prefs" in HOME directory), used to save the project file to load at startup.
     And in configuration window, new buttons to select/clear the default startup project.
   - Same preferences file also used to save/restore windows positions/sizes + open/close states.
   - In free vars, replaced popup window to modify value by new edit widget on right column
     + default format display selected for each var spy, now saved in the project. Done by Heli (helitp AT arrakis DOT es)

v0.9.3 (10 December 2011)
   - Modified serial low-level functions to be used with many ports at the same time (one for modbus master, second
     for monitor "slave" on serial link, third for monitor "master" on serial link).
   - Monitor protocol slave/master can now be used on a serial link, with following frames format:
     STX,...,HEXCRC16a,HEXCRC16b,HEXCRC16c,HEXCRC16d,ETX.
     New parameters available in ".classicladder_prefs" : MONITOR_SLAVE_ON_SERIAL_PORT & MONITOR_SLAVE_SERIAL_SPEED.
     New window "monitor cnx" to choose between UDP and serial port (port allocated at that time just during connect time necessary).
   - Display symbols checkbox for bool vars window (Heli Tejedor idea and patch).
   - Inverted display of "not" coils (very old bug found by Heli). Directly corrected in calc.c
   - New tool "Invert element selected". Contribution by Heli Tejedor.
   - New tool available to "move rung part selected" (usefull sometimes to put it nearly on same place with a little shift!)
   - New "search"/"goto" functions... Initial contribution by Heli, modified for highlights possibilities of element searched, previous
     direction possible, added a new 'Search' menu, search also in sequential sections...
   - Syslog messages of start/end/errors (optional parameter to set in prefs), could be usefull on embedded.
   - Can daemonize (also in prefs). Interesting if compiled without gtk+ interface.
   - Do not try to display (in status bar) ladder element properties on mouse moves, if not a ladder section (broken since v0.8.9)
   - New window for master monitor frames exchanged with target, for that many changes done for threads... Sometime before were
     freezing when disconnected from target in case of timeout (gtk widget call from a thread) ! And again changes to work also
     on Win32...
   - We can now select the top rung not completely displayed ! (annoying before when just few vertical pixels missing!!!)
   - Support for serial even/odd parity & NbrBits & NbrStops parameters on Win32 side (not tested)
   - Can use on priority preferences from "classicladder_prefs" file in current directory near executable (instead of HOME).

v0.9.4 (10 March 2012)
   - Inputs/Outputs read/write port access (x86) now also possible on Windows, using external library "inpout32.dll" for that
     (just need to copy "inpout32.dll" near "classicladder.exe" file).
     http://www.hytherion.com/beattidp/comput/pport.htm
     http://logix4u.net/Legacy_Ports/Parallel_Port/Inpout32.dll_for_Windows_98/2000/NT/XP.html
   - Scan of physical inputs are now filtered, and in another faster task than logic (filtering requiring 3 same states read 1 or 0).
     (created abstract threads (for win/linux/xeno) in a separate file)
   - Fixed monitor not working in serial under Windows (read blocking definitively).
   - Init vars / current states of rungs when starting to connect to a target
   - In frames monitor window, possibility to send any manual frame ask request.
   - Modified low-level serial format of monitor to add an address on one character "@" (you never know!) Not used for now, fixed to '*'.
     STX,@,...,HEXCRC16a,HEXCRC16b,HEXCRC16c,HEXCRC16d,ETX => BEWARE SERIAL FORMAT NOT COMPATIBLE WITH PREVIOUS VERSION...
   - New two tabs in free vars window to display project infos & target infos (only version soft for now).
   - Saving preferences on exit only if GTK interface defined.
   - Added controls on parenthesis for arithm eval.
   - In local harware inputs/outputs, added "DirectPortConfig" type to write config data value to a port (x86). Could be interesting
     for example to configure inputs/outputs directions for somes GPIOs of a card...
   - At start of monitor cnx, request for project infos, to detect mismatch parameters between local<->remote target.
   - Modified JSON get/set clock to use an ascii string instead of int value. Also not compatible with previous version but who cares? ;-)
   - In window "monitor cnx", when "return" pressed (in IP address or serial port/speed), directly connect to target
     (simulate "ok" click).
   - Search variables now also done in compare/operate expressions.
   - Active log event never finished if a "reset" was done (a new one was created).
   - Security added if many ctrl-c received in loops ("end of all" function called only one time)

v0.9.5 (17 August 2012)
   - Project infos not initialized when loading another project (so keeping old datas, if none in the project loaded)
     + renamed project infos as project properties.
   - Load an icon for main window.
   - In config window, modified width sizes of the columns of physical inputs/outputs.
   - Can load/save compressed project with zlib if project name ending with ".clprjz"
   - Transfer of file project compressed from/to target (base64 encoding). Progress bar in main window.
   - Modified cJSON to avoid libm dependancy as no floats number used in the monitor protocol.
   - In monitor, added possibility to reboot system of the target.
     + if running in real-time, get last duration time of logic task.
   - In config, new option in first info tab: "Use real physical & serial modbus inputs/outputs only on the embedded target (not for GTK simul
     interface)". So no more ioperm() attempts for physical i/o ports, and for modbus, do not try to allocate the serial port if not wanted !
   - In new target infos tab (of free vars windows), added kernel and Linux distribution versions taken from "/proc/version" and 
     "/etc/lsb-release" (DISTRIB_DESCRIPTION tag)
   - When selecting "new" project (and a project already loaded), reset current file project name (could be dangerous, if doing "save" after but
     wanted really to do a new project!!! and not deleting previous one...)
   - For an hardware, life led to drive on a port (can be shared with an inputs/outputs port).
   - Fix bug in modbus slave list save.
   - Always save little files of the project with the same order (very usefull in diff between 2 projects files!). Instead to scan temp project
     directory...
   - Reinit general params (task periods, objects sizes) before loading another project (important to have correct default values, if some
     parameters not saved in older project loaded...)
   - Fix Xenomai compilation (broken since previous version for sure?)

v0.9.6 (3 november 2012)
   - Fix many trouble with g_idle_add() usage (a function called by it should return FALSE to be done one time only! else will
     loop!!!)
   - Catched warnings for g_idle_add() functions... (a warning always wants to tell us something! ;-) )
   - In monitor transfer, uncommented and fixed pb 100% cpu usage after displaying message in status bar...(read before!)
   - Review refresh of button "run/stop" from monitor.
   - Second time monitoring, the "run/stop" for the target state was not refreshed.
   - In monitor, can also transfer an embedded archive soft to easily update a target (at end of transfer an update script can
     be launched)
   - Do not refresh status bar, in case of no ladder element properties when mouse outside of current rung (to get displayed
     current message...)
   - Removed 'hardware' word in error messages title (also used for monitor...)
   - Was not stopping logic before doing "new" project. fixed.
   - In monitor, can modify variable value of the target (from window "free var spy").
   - Fixed life led: if not also used in an outputs register + keep logic in stop if no project loaded per default (case for
     embedded).
   - Now Linux sources distributed in a more conventionnal .tar.gz archive instead of a .zip (easier if unzip not installed per
     default on the computer used!)
   - When a program including i/o access transfered (and ioperm() on theses i/o not already done), was crashing when setting
     target in 'run'.
   
v0.9.7 (9 may 2013)
   - Can use RTC device to get time clock (preferences file) + new thread "not real-time" to read time (RTC or standard Linux
     time) then copied in logic thread for system vars (that can be real-time).
     RTC also used in that case when set with monitor.
   - Missing search init (can be seen if search next/previous without new search at start).
   - "Embedded in code" Icons for windows and cursors during edit (Heli Tejedor contribution).
   - Popup menus with right click button available (from an idea of Heli Tejedor) to edit and select element to add.
     Selecting also current element in toolbar window (if opened).  
   - New beautiful toolbar (instead of old basic buttons) in sections manager window (done by Heli Tejedor).
   - New network config window + monitor frames required with target (manual reboot required to be taken into account).
   - Can use in priority preferences file /etc/classicladder_prefs if available.
   - Added current time clock info before each monitor frames displayed.
   - Added scripts to run/update target in the embedded archive.

v0.9.8 (22 june 2013)
   - Get an error message, if abnormal "incomplete" project loaded (we can have the case with ClassicLadder/Windows running
     with an old zlib version, when loading zlib compressed project transfered from target).
     Also now testing if seen end tag line = project file really complete.
   - Fixed bad 'week of day' value on a target using RTC (always sunday).
   - Added max string length test in symbols convert functions.
   - Save in preferences, latest state selected (so that ClassicLadder starts back with project loaded and stopped if wanted,
     instead of always in run after project loaded!)
     When upgrading a target from 0.9.7, you will have to set it to "run" with monitor one time...
   - New %QLEDx variables available. %QLED0 = Green info led on the ClassicLadderPLC hardware.
   - Modbus master stats available for a target added in monitor protocol (just displayed in the console for now).
   - Avoid second launch in run script (test if already running).

v0.9.9 (22 august 2013)
   - When closing config window, do not try to set ioperm() for physical i/o ports if option "Use real physical inputs/outputs
     only on the embedded target" is checked... (reported by Ramin)
   - Label/comment entries disabled per default if no project loaded.
   - Cleanup label/comment datas of an old rung reused when adding a rung (reported by Ramin).
   - With monitor, when setting time even if RTC device selected, also set Linux time clock (usefull for dates on filesystem!)
   - For sequential, if step is an init (or not) displayed in the properties (on a new line property True/False), and can be
     modified directly here (better as some bugs remaining in the edit when deleting elements...)
   - For trying to advance debug in sequential edit, displaying internal infos of transitions in properties (steps lists can be
     manually edited)
   - Properties strings that are not editable are now disabled!
   - Monitor timeout for serial now use same time than in IP (longer only when transfering files).
   - 38400 serial speed per-default in monitor window.
   - In sequential, bad highlight of transition with %B0 variable.
   - Sequential, now 32x32 elements (instead of 16x16). Does not cost any more memory (steps and transitions stored with coords)
   - Sequential, destroy step ok (for transitions) if not first one of many steps activated simultaneously.
   - Sequential, now correctly displaying jump cross step where there is a transition on top of it (as in Ramin'water example)
   - Monitor slave in serial, not pause if some characters received (even if frame not complete).

v0.9.10 (31 october 2013)
   - Modified some debug messages in serial port functions.
   - Optimized monitor protocol frames to be shorten (less bytes to exchange). interesting on serial link...
     Frames the most exchanged now rendered in JSON without format (no space, no tab, no CR... a little harder to read if needed
     but really shorter!)
     Theses frames are no more compatible with previous version, you will have to upgrade embedded target version to be able to
     continue to monitor it!!!)
   - In monitor, if a 'unknown' request asked received, now the slave add "ReqError" attribute so that the master knows that the
     request has not been treated... 
   - Calc rung, only lines used (also now used by monitor).
   - Set/unset inputs/outputs: new buttons for that in bools vars window, blue backgroud when a var is setted. When connected to
     a target, do it on it with a new monitor frame!).
   - On rungs, displaying with blue rectangle elements which var is setted.
   - Modified end (removed exit() in function to end all).
   - After monitor decnx of the target, init all vars and update display.

v0.9.11 (31 january 2014)
   - In monitor connect window, added timeout widget entry to adjust timeout wait reply (usefull when using low serial
     speed, modem, ...)
   - Can use PSTN/GSM dialup modem with Hayes AT commands for serial monitor (on slave serial link, and for master connection).
   - New tab "modem" in configure window to set AT sequences for slave monitor and master connect. 
   - New menu entry, to be able to run logic only for one cycle, or freeze current running (when freezed, current rung status
     displayed).
   - Added exit() in end with ctrl-c handler. Else was crashing on exit.
   - If no coil on rungs, and set/unset vars, blue background not refreshed to know that the var is setted. Fixed.
   - If previous project monitored has modbus slaves, and current project none, at a time, monitor asking modbus slaves stats in
     loop, and nothing more. Fixed.
   - New window to set monitor serial config (port name, speed). port can be blank to let a port available for other function
     (modbus master, future modbus slave?, ...)
     For this, added new monitor frame to be able to set speed and serial port name.
     (easier instead of having to edit 'classicladder_prefs' file with vi in telnet!)
   - In Gtk config window, new option in 'misc' tab, to auto adjust summer/winter time.
   - Moved from monitor protocol code to set time clock (Linux & Rtc) in a function to be used per auto summer/winter time
     + code to do summer/winter time switch.

v0.9.12 (10 May 2014)
   - Bug with monitor protocol, if setting to 0 an input/output already setted to 1, or setting to 1 one already to 0. fixed.
   - Save/load events log datas (in binary) + mutex added + cleanup after parameters loaded
     +  when connected to a target can be transfered with monitor file transfer (csv/text gz compressed file save).
   - Added buttons "Refresh", "Export to csv" and "Clean up" (with monitor frame required if target connected) in log events window.
   - Save in preferences, check state of 'Display Symbols' in the windows.
   - I/O conf file read, added security if one day more line than planned in memory.
   - Added infos project site & company.
   - SMS & emails remotes alarms. For email, external command "mailsend" required in /usr/bin (https://code.google.com/p/mailsend/)
     SMS send tested with a Wavecom chip modem.
   - Longuer comment possible in rungs (29 chars (more exactly bytes!) before, now 99). If used, new tag in file save to not overflow
     in old versions + fixed possible overflow with accents characters using 2 bytes...
   - Arithm eval (in operator block), character conflict with '^' used for new xor (added in v0.5.5), but it was already used as POW
     operator... Now POW(a,b) is a function and gives correct results ! Reported by Salvatore.
   - Added Xenomai version (if available) for target infos.
   - Updated cJSON library with latest version available on SourceForge, and modified again to not use float/double numbers
     (to avoid libm dependancy).
   - Config hardware for outputs done, even if checked that should be done only for target. Fixed.
   - Modified to avoid a mode switch under Xenomai with new/end event (with get time).

v0.9.013 (30 May 2014)
   - For remote alarms, bug and limit in global flags sets: SMS only usable on slot '0', and emails on slot '1'...
     was just working ok with my first example! fixed.
   - Initial pipe/fork added at start of the main to launch external command like 'mailsend'
     (else not working when embedded with Xenomai)
   - To send email, use login/password authentification if defined (parameters never used before...)
   - Optional code PIN can be used with modem (for both slave and master).
   - Added 2 DNS servers in network config.
   - Little code rework: added function InitRung() common for InitRungs() and InitBufferRungEdited()
   - Init sequential elements (steps/transitions/comments) of a page deleted.

v0.9.014 (27 September 2014)
   - When starting with project not existing (setted in prefs), version param string corrupted. Bad init fixed.
   - 4 frames log windows more (for modbus/master and monitor slave IP&serial and modbus/slave)
     + added 'Copy to clipboard' button.
   - frames log buffers added (that can be read from a connected target with an exported ascii/gz file, very usefull to look at
     what is exchanged on embedded target! before we had to relaunch classicladder in a telnet session and look at printf messages...).
   - when target connected 'clean up' in frames log window reuse JSON frame "CleanUp" request (before only for the 'events log')
   - problem if --version or --help parameters in line command, were not exiting correctly (missing LaunchExternalCmd child end).
   - Added RaspberryPI GPIO access, new "Raspberri_GPIO" type in I/O config window,
     using WiringPi library. Pull-up activated on inputs.
   - Added I/O config lines (from 5 to 15), as with RaspberryPI generally one line = one GPIO (not consecutives)

v0.9.020 (2 January 2015)
   - Cleanup of monitor log not working on target. bad speed test with one exec (so not really tested in fact...) in previous version, corrected.
   - Totally review malloc/free datas, splitted static infos and dynamic sized project datas, so that can be used after each project load...
     set NULL each pointer after free() call done on it (and discovered following bug with that...) !
     Rewrite to avoid double malloc/mbuff_alloc/free huge parts, but using one commun functions MyMalloc/MyFree.
     So now each time a project is loaded we alloc datas with size configured for each in the project !
     (Be carefull, not adapted for running in a "old-school" rt kernel RTLinux/RTAI module! but by the way, were still working?
      No more used since a long time (for me...) since Xenomai!!!)
   - After project loaded, take into account new periodic values configured for tasks.
   - Found a bug at gtk exit (reading InfosGene data just freed before !!! now segfault with NULL pointer on memory freed)
   - Bug when adding/inserting a rung with no more free rung available... now display an error message, and no more in edit mode !
   - In target infos tab (of free vars windows), added embedded disk use/free/size (#DEFINE EMBEDDED_DISK_DEVICE_STATS in classicladder.h)
   - When reading set vars list, added a security to verify if not a var no more allocated in this project (out of size nbr inputs
     or outputs configured)
   - In config window for nbr elements, added memory sizes of elements after "current alloc=".
   - When connected to a target: added warning message when close main window asked, and new/load project no more allowed !
   - Modbus/master slave stats, moved its display at bottom on new modbus/master monitor window.
   - Also open config window no more allowed when connected to a target (now that modbus stats displayed moved...)
   - Failed to reveive a project from a target if no project loaded before (missing TmpDirectoryRoot init).
     Now InitTempDir directly done at startup (before only loading/saving a project).
   - In modbus/master, for each slave added stats average of nbr frames exchanged per second.

v0.9.021 (24 January 2015)
   - Error on latest version released in 'DEBUG' so very big executables and without SEGV trap...
     (DEBUG not put in comment in Makefile).
     Added a #warning if compiled in DEBUG now ! ;-)
   - Modified bad decimal calc stats frames/secs average for modbus/master.
   - Modified default GPIOs used for life/use LED on RaspberryPI, 27 and 17.
   - In monitor server, modified select( 16, => FileDecrip+1, ... somes should be done elsewhere
   - Now using wheel mouse and up/down, pages up/down and home/end keys to scroll view. Initial contribution by Heli Tejedor.
     Shortcuts for right popup menu with ALT+xx keys.
   
v0.9.022 (14 May 2015)
   - For project infos, added comment field with possible multi lines.
   - New functions blocks 'Registers' (stacks) to store/unstore many words values series in FIFO or LIFO order.
     2 new sizes parameters : nbr registers + queue size per each register (same for each).
     Data series stored in each register could be downloaded from menu View/RegisterContent => open a requester to type
     register number => download csv file and open it with default application.
   - For transfer file, added a sub-number file info (for register number choice with one type file).
   - Added a warning message if in current edit modifications, and project save asked !
   - If in 'stop', stats for modbus/master slaves where not correctly initialized (strangely was crashing when recompiled
     for Raspberry ! reported by Martin Krusarov)
     Now doing an init on stats when switched in 'run'.
   - Many abnormals SEGV on exit. fixed (perhaps? caused by free() with NULL on pointer done in v0.9.020) by adding a little
     pause before really ending (time to let running threads to exit)
   - In vars_system.c, ioctl( DeviceRTC, RTC_SET_TIME, tm_return => &tm_result )
   - In edit.c, for FullDeleteElement(), SEGV when deleting an element on third line (y=2) and last column (during verticals
     links cleanup).
   - Also modified select( 16, => FileDecrip+1, ... for serial (Linux) and modbus/master (socket/IP).

v0.9.023 (30 May 2015)
   - Moved all sources files and Makefile in a new sub-directory "sources"
     (easier now for users to see txt docs presents in the root ! yes they are somes...)
   - Trouble with vars %S40 & %S41 (system vars for modem) displayed as "???" because of bad init in vars names list array, fixed.
   - Display trouble on sizes tab in config window, fixed.
   - Sometime could crash when displaying log book events just transfered from target (because not using g_idle_add() in a thread !)

v0.9.030 (31 December 2015)
   - Compatibility with Xenomai 3 (Cobalt), big rework for tasks init and set cycle period using timerfd for Linux and Xenomai3
     (based on my "demo_periodic_thread_posix.c" example)
   - Added near logic task duration of last scan, info of nbr ticks missed in 'logic' periodic task when running in real-time.
   - wiringSam library use for I/O hardware (for Atmel AT91Sam9 as in the Arietta of AcmeSystems), files directly added to the project.
   - Do not add CR after CTRL-Z marker for end of SMS text (should not be send, and can trouble some models of modems differents than
     good old Wavecom used and tested).
   - For print, sequential wasn't using full width page.
   - For print, not only "current section" but also "all sections" choice possible. new options tab added in printer window.
   - Can print new pages with symbols defined list, added in the new print options tab.
   - If no project loaded at startup, sections manager list was not updated (to have default ladder "Prog1" displayed), and so could
     crash if someone have the bad idea to click on "properties" ! ;-)
   - Fixed possible crash on exit in modbus master thread if InfosGene (pointer) read but already freed (NULL).
   - Renamed "sources" directory as "src" (because I've already another parent "sources" directory, so easier for me...!)
   - Added vertical scrollbars on some tabs in config window and add 2 subs tabs with just one main "physical I/O" tab.
   - After loading a project, set vertical lift of symbols window at top.

v0.9.100 (9 August 2016)
   - GTK3 compatibility modifications
     * replace many (GtkSignalFunc)function by GTK_SIGNAL_FUNC(function)
     * use gtk_dialog_get_content_area(GTK_DIALOG(pDialogBox)) instead of direct vbox access in GTK_CONTAINER(GTK_DIALOG(pDialogBox)->vbox)
     * use gtk_dialog_get_action_area(GTK_DIALOG(pDialogBox)) instead of direct vbox access in GTK_CONTAINER(GTK_DIALOG(pDialogBox)->action_area)
     * gtk_tooltips deprecated, replaced with direct and easier gtk_widget_set_tooltip_text( )
     * function declaration SignalPageSelected( GtkNotebook * notebook, "GtkNotebookPage" => GtkWidget * page, guint page_num, gpointer user_data )
     * in manager window, switched old obsolete gtk_clist to use gtk_tree_view...
     * some gtk_signal_connect_object() => g_signal_connect_swapped()
     * gtk_combo_box_text replacements (just #define for now in 'classicladder_gtk.h'... so that can still be compiled on GTK versions < 2.24)
     * gtk_widget_set_usize() => gtk_widget_set_size_request() or deleted...
     * in edit toolbar, if compiled for GTK3 using Cairo Surface instead of GdkPixmap to create gtk_image displayed in radio buttons.
     * and many others (easy) deprecated (directs) replacements by viewing Gtk "V2" documentation !
       for now, SOME REPLACEMENTS ARE JUST DONE WITH #define see classicladder_gtk.h - to clean-up in the future !
     * lot of warnings presents...! but well that's compiling!!!
     Included precompiled Linux binary executable still compiled with GTK+2, see README.txt if you want to compile a GTK3 version !
   - Reorganized Period/Sizes/Info tab in config window (major parameters on 2 columns + vertical separator, to have window less high).
   - Added optional #define HARD_LIFE_USERS_LEDS_CMD_INVERTED in classicladder.h to choose logic command of leds (inverted on my x86 PLC).
   - Fixed bug after doing "new project" impossible to edit because project not refreshed (state in "load") if not a run/stop done... since v0.9.020?
   - When "edit" in popup menu selected, put edit window in front (usefull if hidden...)
   - Sequential not displayed with active elements colored if in edit or not running (as already done in ladder).
   - Size of elements in sequential sized from 16x16 to 32x32 so that all cases could be used without overlap (for variables
     transitions displayed).
   - Fixed cosmetic sequential bugs when drawing steps activated/desactivated simultaneously if left step not aligned with top
     transition (seen in example_sequential_3).
   - For sequential can now reuse 'comment' field entry, only ladder before.
   - Added maximum scan execution for real-time stats.
   - In log events windows, added at the bottom a checkbox option "show only active events" (ones not ended).
   - On target monitor timeout on master, added asks resets to avoid for example a waiting reboot at next connexion ! :-(
     (seen during hard reboot test of the Arietta card, without reply frame sended and so received...)
   - Added "halt target" menu (with sub-menu reboot/halt) + modified exiting monitor request command "RebootTarget" with new halt
     option parameter.
   - Possibility to use "telinit" command (with "6" or "0" parameter) to reboot/halt the system. New define available
     USE_TELINIT_TO_REBOOT_HALT in classicladder.h + different signal on init when using it for "halt" (instead of default "reboot").
   - For bools vars spys window switched layout to table (instead of many vbox/hbox).
   - When start to edit, close search bar. And during edit, not authorized to open...
   - When using rtc device to read time (set in classicladder_prefs), if failed to open "/dev/rtc", try with "/dev/rtc0" (Arietta).
   - Outputs coils with their var "setted" were not displayed with the blue rectangle element on it and real state...
   
v0.9.101 (18 March 2017)
   - Fixed random crash at startup if menu "view symbols windows" was checked (saved in .classicladder_prefs) - Because of
     NBR_SYMBOLS undefined, now create blank lines when intializing window with NBR_SYMBOLS_DEF constant.
   - In function POW(a,b), should return 1 if b=0. Now corrected. Reported by Salvatore, thanks to him.
   - In monitor for distrib Linux version of target, if "/etc/lsb-release" not available, read "/etc/debian_version" instead.
   - Removed USE_TELINIT_TO_REBOOT_HALT define from classicladder.h to select between "telinit" or signals (with kill()) on
     init process to reboot/halt the embedded target, and directly use "telinit" command if available.
   - Inverted GPIO leds (life/user) in classicladder.h for ClassicLadder_PLC_Arietta hardware card.
   - Can now modify /etc/hostname in network config window (usefull for Bonjour discovery service)
   - Replaced checkboxes for outputs by lamps off/on images (looks like an "out" and not an "in", without possible user
     action...) - Judicious suggestion by Philippe Lagarde for learners students.
   - At startup afted loaded events log, write "1" to bool vars with corresponding pendings events (not previously finished).
     Done to avoid to end/create a new event at startup...
   - Added menu View / "Syslog Linux debug" (uncompressed transfer, and then opened as text file with external associated
     utility)

v0.9.110 (26 August 2017)
   - Internationalization ("i18n") of GTK interface of ClassicLadder done ! French language added for now. Help welcome for
     others languages if you have time... Well, lot of strings to translate. More than I would imagine...!
   - Longer timeout when setting clock time with monitor protocol.
   - Added new files "target_embedded_plc_486.h", "target_embedded_raspberrypi.h" & "target_embedded_plc_arietta.h" used to
     avoid manual editing of "classicladder.h" before compilation for an embedded target
   - Created external application 'ClassicLauncher' for external commands to launch (update script/reboot/halt/mailsend),
     because of trouble if compiled for Xenomai 3 (when fork to launch execv)
   - Created new file "time_and_rtc" with parts from tasks.c & vars_systems.c, to be used in new 'time_test' application.
     Fixed bad time if using RTC in UTC time (with local hours offset).
     In monitor, send set clock in UTC time instead of local time.
   - Modify of sections not authorized if in 'run' or 'connected to a target'.
   - Modify of project propertied not authorized if 'connected to a target'.

v0.9.111 (10 March 2018)
   - Modifications for MSYS2/Windows (with GTK3), added bind_textdomain_codeset() with "UTF-8" required for Internationalization!
     and removed some old gdk_threads_enter()/gdk_threads_leave() not necessary and strangely crashing application when
     moving/resizing a window !!!
   - Review get/set variable error for a slave in modbus/master (functions).
   - Confirmation ask request before doing "cancel", if current ring/sequential has been modified...
   - In config window, for communication modbus parameters tab, switched layout to table.
   - When symbol edited, directly redisplay labels bool vars windows if symbols displayed...
   - Hide label rung when a sequential section is displayed.
   - Display new comment entry for sequential at top of the page when printed...
   - Fixed, always first sequential page was printed for each section !

v0.9.112 (6 April 2018)
  - Fixed very annoying random crash under Windows/GTK3 under simulation, with bad day value for date/time display in
    free vars window (localtime function datas output). Modified with a copy struct tm + mutex.
  - When printing sequential under Windows, bad height of the rectangle including comment at the top of the page. Fixed.
  - On bools and free vars window, set size width char on entries for less larger windows.

v0.9.113 (4 January 2020)
  - In arithm expressions, constants can now also be used in functions + if function name not recognized, tell it instead of
    generic error at end + in general also display position string where the error occured.
    + new classic functions available to shift words : SHL() & SHR() & ROL() & ROR() with system %S8 for out bit (thanks
      to Franco Sabbaini for the first modifications).
    + no more segmentation fault in case of divide per zero (also reported by Franco Sabbaini) : instead return 0, set
      system %S7, and red backgroud for the first rung where the error occured (a little easier to found where...) !
      New rung error number info (for red background drawing) also added in protocol monitor exchange.
   - New variables browser window available. To see all available variables, and easily select a variable instead of manual
     typing. Used for free vars window, and in properties window for variables and arithmetic expressions.
   - If editor window closed, and "modifying" from popup menu, was opening editor window without checking menu "View / Editor window". fixed.
     + now also open editor window when adding or inserting a rung.

v0.9.114 (.. March 2023)
   - When opening the variables browser, if already a var is present in the edit widget, display the corresponding combo type
     and select it per default in the list.
     + can double-click on a var in the list to validate variable selected (instead of clicking "OK" button, faster!).
   - When opening the variables browser for "variable" properties, if string actually ending with '[' character, add variable selected
     instead of replacing it (usefull to add a second index variable between [ ]).
   - At first startup (no ".classicladder_prefs" file in home), booleans & free vars windows are opened, but abnormally not checked in
     the view menu ! And also always opened at startup even if closed when quitting. Fixed.
   - To avoid (justified) warnings with new gcc version, replace '400' value constant per LGT_FOR_PATH_AND_FILE define + some mallocs done.
   - Remove some warnings with gcc 64 bits compilation (till now, always in 32 bits...). Use of GINT_TO_POINTER() & GPOINTER_TO_INT() macros.
   - Added in preferences choice of the font name / size used to draw in the main window. Font chooser dialog only available
     on Gtk+3 version. Default size 9, now with larger screen resolution, the default little 8 is no more adapted...
   - Review interface to have in main window the current section name displayed in a combo box on the right and "selectable" from here
     + move "sections" button toolbar righter and add before a separator.
   - Section manager window not opened per-default now. To have another window at less at startup (not really useful when editing
     just one ladder section for example, and also after defined all sections wanted) !
   - Many havardAasen (git) modifications pulls (requests dialogs code simplifications, Gtk warkings, Clang warnings, typos, ...).
   - On 'quit', hide immediately all windows opened, before waiting pause added (to wait end of threads). Better impression for user.
   - Fixed bugs with keys / mouse scroll for vertical scroll bar not blocked when editing rung section (reported by havardAasen)
     + can use left/right keys shortcut for sequential horizontal scroll and vertical not blocked during sequential edit and scroll mouse
     usable for sequential also!
   - Edits shortcuts also usable for sequential sections.
   - Possible error messages "!!!Abnormal current type=xxxxxx in rung..." if copying/moving rung part without any selection done before !
     Now displaying an error message if no rung part selected before copying/moving.
   - Completed french language translations with latests missing strings (since variables brower and font selection added).
   - Removed many Gtk+3 deprecated warnings with gtk_hbox_new() & gtk_vbox_new(), by using new gtk_box_new() if available (only Gtk+3!)...

