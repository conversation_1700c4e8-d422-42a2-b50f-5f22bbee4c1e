void InitRungs(void);
void InitRung( StrRung * pRung );
void PrepareRungs(void);
void InitTimers(void);
//void PrepareTimers(void);
void InitMonostables(void);
//void PrepareMonostables(void);
void InitCounters(void);
//void PrepareCounters(void);
void InitTimersIEC(void);
//void PrepareTimersIEC(void);
void InitRegisters();
void PrepareAllDatasBeforeRun(void);
void InitArithmExpr(void);
int ReadVarForElement( StrElement * pElem, char * pInfoVarIsSet );
void WriteVarForElement( StrElement *pElem, int Value );
void RefreshASection( StrSection * pSection );
void ClassicLadder_RefreshAllSections(void);
void CopyRungToRung(StrRung * RungSrc,StrRung * RungDest);
void CopyElementToElement( StrElement * pEleSrc, StrElement *pEleDest );
char SaveRegisterFunctionBlockContent( int RegisterNbr, char * File, char Compressed );
