{"file:///c%3A/home/<USER>/SoftPLC/classicladder-main/wiringSam/Makefile": {"language": "<PERSON><PERSON><PERSON>", "code": 14, "comment": 3, "blank": 9}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_system.h": {"language": "C++", "code": 3, "comment": 0, "blank": 4}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/wiringSam/wiringSam.h": {"language": "C++", "code": 80, "comment": 25, "blank": 29}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/wiringSam/test_at91sam_gpio.c": {"language": "C", "code": 28, "comment": 17, "blank": 8}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_names.h": {"language": "C++", "code": 4, "comment": 0, "blank": 4}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/wiringSam/wiringSam.c": {"language": "C", "code": 163, "comment": 64, "blank": 35}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_names_list.c": {"language": "C", "code": 77, "comment": 25, "blank": 28}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_browser_gtk.h": {"language": "C++", "code": 4, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_system.c": {"language": "C", "code": 138, "comment": 34, "blank": 18}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_mat.c": {"language": "C", "code": 70, "comment": 29, "blank": 14}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_browser_gtk.c": {"language": "C", "code": 246, "comment": 45, "blank": 33}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_access.c": {"language": "C", "code": 381, "comment": 37, "blank": 17}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_access.h": {"language": "C++", "code": 12, "comment": 1, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/vars_names.c": {"language": "C", "code": 680, "comment": 65, "blank": 41}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/time_and_rtc.c": {"language": "C", "code": 208, "comment": 39, "blank": 17}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/time_and_rtc.h": {"language": "C++", "code": 14, "comment": 1, "blank": 6}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/tasks.h": {"language": "C++", "code": 25, "comment": 1, "blank": 12}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/tasks_OLD_beforeXeno3.c": {"language": "C", "code": 403, "comment": 36, "blank": 31}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/time_test_rtc.c": {"language": "C", "code": 44, "comment": 4, "blank": 8}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/target_embedded_raspberrypi.h": {"language": "C++", "code": 4, "comment": 9, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/symbols_gtk.h": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/tasks.c": {"language": "C", "code": 499, "comment": 45, "blank": 39}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/target_embedded_plc_486.h": {"language": "C++", "code": 11, "comment": 10, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/symbols.h": {"language": "C++", "code": 4, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/target_embedded_plc_arietta.h": {"language": "C++", "code": 8, "comment": 7, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/symbols_gtk.c": {"language": "C", "code": 194, "comment": 47, "blank": 30}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/socket_server.h": {"language": "C++", "code": 3, "comment": 0, "blank": 4}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/symbols.c": {"language": "C", "code": 142, "comment": 48, "blank": 14}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/spy_vars_gtk.h": {"language": "C++", "code": 14, "comment": 1, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/socket_modbus_master.h": {"language": "C++", "code": 6, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/spy_vars_gtk.c": {"language": "C", "code": 883, "comment": 173, "blank": 95}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/socket_modbus_master.c": {"language": "C", "code": 369, "comment": 57, "blank": 40}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/socket_server.c": {"language": "C", "code": 215, "comment": 38, "blank": 28}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/serial_win.c": {"language": "C", "code": 171, "comment": 40, "blank": 21}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/serial_linux.c": {"language": "C", "code": 249, "comment": 44, "blank": 24}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/search.h": {"language": "C++", "code": 9, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/serial_common.h": {"language": "C++", "code": 17, "comment": 2, "blank": 4}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/protocol_modbus_slave.h": {"language": "C++", "code": 4, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/sequential.h": {"language": "C++", "code": 58, "comment": 44, "blank": 22}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/serial_test_rs485.c": {"language": "C", "code": 59, "comment": 4, "blank": 10}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/search.c": {"language": "C", "code": 378, "comment": 40, "blank": 35}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/protocol_modbus_slave.c": {"language": "C", "code": 255, "comment": 53, "blank": 21}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/print_gtk.h": {"language": "C++", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/protocol_modbus_master.h": {"language": "C++", "code": 70, "comment": 9, "blank": 12}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/print_gnome.h": {"language": "C++", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/protocol_modbus_master.c": {"language": "C", "code": 762, "comment": 74, "blank": 32}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/protocol_modbus_defines.h": {"language": "C++", "code": 24, "comment": 3, "blank": 8}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/print_gtk.c": {"language": "C", "code": 273, "comment": 28, "blank": 39}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/network_config_window_gtk.h": {"language": "C++", "code": 2, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/print_gnome.c": {"language": "C", "code": 174, "comment": 27, "blank": 40}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/preferences.c": {"language": "C", "code": 468, "comment": 33, "blank": 19}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/preferences.h": {"language": "C++", "code": 16, "comment": 0, "blank": 5}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_windows_gtk.h": {"language": "C++", "code": 23, "comment": 2, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/network_config_window_gtk.c": {"language": "C", "code": 95, "comment": 23, "blank": 16}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_threads.h": {"language": "C++", "code": 7, "comment": 1, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_transfer.h": {"language": "C++", "code": 27, "comment": 0, "blank": 10}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_windows_gtk.c": {"language": "C", "code": 530, "comment": 102, "blank": 58}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_transfer.c": {"language": "C", "code": 635, "comment": 80, "blank": 28}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_threads.c": {"language": "C", "code": 606, "comment": 69, "blank": 39}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_sockets_udp.h": {"language": "C++", "code": 41, "comment": 0, "blank": 14}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_serial_config_window_gtk.h": {"language": "C++", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_sockets_udp.c": {"language": "C", "code": 115, "comment": 33, "blank": 17}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_serial_config_window_gtk.c": {"language": "C", "code": 87, "comment": 23, "blank": 15}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_protocol_adds_serial.h": {"language": "C++", "code": 17, "comment": 0, "blank": 10}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_protocol.h": {"language": "C++", "code": 36, "comment": 0, "blank": 11}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_protocol_adds_serial.c": {"language": "C", "code": 77, "comment": 55, "blank": 11}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/module_rtlinux.h": {"language": "C++", "code": 1, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/modem.h": {"language": "C++", "code": 10, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/modem.c": {"language": "C", "code": 287, "comment": 46, "blank": 26}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/module_rtlinux.c": {"language": "C", "code": 71, "comment": 19, "blank": 19}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/monitor_protocol.c": {"language": "C", "code": 1171, "comment": 232, "blank": 118}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/module_rtai.c": {"language": "C", "code": 95, "comment": 29, "blank": 16}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/matplc.conf": {"language": "Properties", "code": 7, "comment": 8, "blank": 8}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/manager_gtk.h": {"language": "C++", "code": 13, "comment": 1, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/menu_and_toolbar_gtk.h": {"language": "C++", "code": 20, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/menu_and_toolbar_gtk.c": {"language": "C", "code": 535, "comment": 42, "blank": 40}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/manager_gtk.c": {"language": "C", "code": 545, "comment": 96, "blank": 56}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/manager_gtk_OLD_CLIST.c": {"language": "C", "code": 382, "comment": 75, "blank": 41}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/log_events_gtk.h": {"language": "C++", "code": 6, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/manager.h": {"language": "C++", "code": 13, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/manager.c": {"language": "C", "code": 283, "comment": 41, "blank": 24}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/icons_gtk.h": {"language": "C++", "code": 3, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/log_events.h": {"language": "C++", "code": 22, "comment": 0, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/Makefile": {"language": "<PERSON><PERSON><PERSON>", "code": 381, "comment": 70, "blank": 106}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/log_events_gtk.c": {"language": "C", "code": 296, "comment": 51, "blank": 42}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/hardware.h": {"language": "C++", "code": 9, "comment": 4, "blank": 7}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/log_events.c": {"language": "C", "code": 673, "comment": 97, "blank": 38}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/ladderlib.c": {"language": "C", "code": 112, "comment": 26, "blank": 16}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/icons_gtk.c": {"language": "C", "code": 134, "comment": 32, "blank": 18}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/hardware.c": {"language": "C", "code": 589, "comment": 62, "blank": 41}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/global.h": {"language": "C++", "code": 69, "comment": 20, "blank": 25}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files_sequential.h": {"language": "C++", "code": 2, "comment": 0, "blank": 2}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/frames_log_buffers.c": {"language": "C", "code": 192, "comment": 30, "blank": 21}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files_project.h": {"language": "C++", "code": 12, "comment": 0, "blank": 4}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/frames_log_buffers.h": {"language": "C++", "code": 26, "comment": 4, "blank": 10}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files_project.c": {"language": "C", "code": 401, "comment": 111, "blank": 41}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_sequential.h": {"language": "C++", "code": 7, "comment": 0, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files.h": {"language": "C++", "code": 49, "comment": 3, "blank": 10}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_sequential.c": {"language": "C", "code": 1018, "comment": 72, "blank": 52}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_gtk.h": {"language": "C++", "code": 51, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files.c": {"language": "C", "code": 2030, "comment": 158, "blank": 93}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/editproperties_gtk.h": {"language": "C++", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_copy.h": {"language": "C++", "code": 7, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_gtk.c": {"language": "C", "code": 694, "comment": 76, "blank": 48}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit_copy.c": {"language": "C", "code": 158, "comment": 27, "blank": 15}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/editproperties_gtk.c": {"language": "C", "code": 240, "comment": 108, "blank": 34}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/drawing_sequential.h": {"language": "C++", "code": 4, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit.h": {"language": "C++", "code": 28, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/edit.c": {"language": "C", "code": 1340, "comment": 161, "blank": 71}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/files_sequential.c": {"language": "C", "code": 217, "comment": 26, "blank": 14}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/drawing_sequential.c": {"language": "C", "code": 507, "comment": 82, "blank": 32}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/drawing.h": {"language": "C++", "code": 18, "comment": 0, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/config_gtk.h": {"language": "C++", "code": 2, "comment": 0, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/config.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/drawing.c": {"language": "C", "code": 1027, "comment": 193, "blank": 58}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/classiclauncher.c": {"language": "C", "code": 122, "comment": 13, "blank": 15}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/config.c": {"language": "C", "code": 84, "comment": 26, "blank": 15}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/classicladder_gtk.h": {"language": "C++", "code": 72, "comment": 2, "blank": 6}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/config_gtk.c": {"language": "C", "code": 1468, "comment": 216, "blank": 84}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/classicladder.h": {"language": "C++", "code": 670, "comment": 94, "blank": 102}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/classicladder.c": {"language": "C", "code": 733, "comment": 101, "blank": 72}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/calc_sequential.h": {"language": "C++", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/cJSON.c": {"language": "C", "code": 480, "comment": 70, "blank": 70}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/cJSON.h": {"language": "C++", "code": 80, "comment": 49, "blank": 28}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/classicladder_gtk.c": {"language": "C", "code": 1511, "comment": 268, "blank": 140}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/calc_sequential.c": {"language": "C", "code": 140, "comment": 30, "blank": 16}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/base64.h": {"language": "C++", "code": 2, "comment": 5, "blank": 3}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/arithm_eval.h": {"language": "C++", "code": 8, "comment": 0, "blank": 6}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/calc.c": {"language": "C", "code": 937, "comment": 85, "blank": 39}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/calc.h": {"language": "C++", "code": 17, "comment": 4, "blank": 1}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/arrays.c": {"language": "C", "code": 529, "comment": 67, "blank": 60}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/base64.c": {"language": "C", "code": 72, "comment": 52, "blank": 28}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/src/arithm_eval.c": {"language": "C", "code": 728, "comment": 112, "blank": 47}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/old_files/gdk_includes_update/gdkkeysyms.h": {"language": "C++", "code": 2181, "comment": 33, "blank": 8}, "file:///c%3A/home/<USER>/SoftPLC/classicladder-main/old_files/gdk_includes_update/gdkkeysyms-compat.h": {"language": "C++", "code": 2178, "comment": 26, "blank": 5}}