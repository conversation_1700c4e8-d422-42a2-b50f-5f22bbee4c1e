"filename", "language", "Makefile", "C++", "C", "Properties", "comment", "blank", "total"
"c:\home\project\SoftPLC\classicladder-main\old_files\gdk_includes_update\gdkkeysyms-compat.h", "C++", 0, 2178, 0, 0, 26, 5, 2209
"c:\home\project\SoftPLC\classicladder-main\old_files\gdk_includes_update\gdkkeysyms.h", "C++", 0, 2181, 0, 0, 33, 8, 2222
"c:\home\project\SoftPLC\classicladder-main\src\Makefile", "Makefile", 381, 0, 0, 0, 70, 106, 557
"c:\home\project\SoftPLC\classicladder-main\src\arithm_eval.c", "C", 0, 0, 728, 0, 112, 47, 887
"c:\home\project\SoftPLC\classicladder-main\src\arithm_eval.h", "C++", 0, 8, 0, 0, 0, 6, 14
"c:\home\project\SoftPLC\classicladder-main\src\arrays.c", "C", 0, 0, 529, 0, 67, 60, 656
"c:\home\project\SoftPLC\classicladder-main\src\base64.c", "C", 0, 0, 72, 0, 52, 28, 152
"c:\home\project\SoftPLC\classicladder-main\src\base64.h", "C++", 0, 2, 0, 0, 5, 3, 10
"c:\home\project\SoftPLC\classicladder-main\src\cJSON.c", "C", 0, 0, 480, 0, 70, 70, 620
"c:\home\project\SoftPLC\classicladder-main\src\cJSON.h", "C++", 0, 80, 0, 0, 49, 28, 157
"c:\home\project\SoftPLC\classicladder-main\src\calc.c", "C", 0, 0, 937, 0, 85, 39, 1061
"c:\home\project\SoftPLC\classicladder-main\src\calc.h", "C++", 0, 17, 0, 0, 4, 1, 22
"c:\home\project\SoftPLC\classicladder-main\src\calc_sequential.c", "C", 0, 0, 140, 0, 30, 16, 186
"c:\home\project\SoftPLC\classicladder-main\src\calc_sequential.h", "C++", 0, 3, 0, 0, 0, 1, 4
"c:\home\project\SoftPLC\classicladder-main\src\classicladder.c", "C", 0, 0, 733, 0, 101, 72, 906
"c:\home\project\SoftPLC\classicladder-main\src\classicladder.h", "C++", 0, 670, 0, 0, 94, 102, 866
"c:\home\project\SoftPLC\classicladder-main\src\classicladder_gtk.c", "C", 0, 0, 1511, 0, 268, 140, 1919
"c:\home\project\SoftPLC\classicladder-main\src\classicladder_gtk.h", "C++", 0, 72, 0, 0, 2, 6, 80
"c:\home\project\SoftPLC\classicladder-main\src\classiclauncher.c", "C", 0, 0, 122, 0, 13, 15, 150
"c:\home\project\SoftPLC\classicladder-main\src\config.c", "C", 0, 0, 84, 0, 26, 15, 125
"c:\home\project\SoftPLC\classicladder-main\src\config.h", "C++", 0, 1, 0, 0, 0, 1, 2
"c:\home\project\SoftPLC\classicladder-main\src\config_gtk.c", "C", 0, 0, 1468, 0, 216, 84, 1768
"c:\home\project\SoftPLC\classicladder-main\src\config_gtk.h", "C++", 0, 2, 0, 0, 0, 1, 3
"c:\home\project\SoftPLC\classicladder-main\src\drawing.c", "C", 0, 0, 1027, 0, 193, 58, 1278
"c:\home\project\SoftPLC\classicladder-main\src\drawing.h", "C++", 0, 18, 0, 0, 0, 3, 21
"c:\home\project\SoftPLC\classicladder-main\src\drawing_sequential.c", "C", 0, 0, 507, 0, 82, 32, 621
"c:\home\project\SoftPLC\classicladder-main\src\drawing_sequential.h", "C++", 0, 4, 0, 0, 0, 3, 7
"c:\home\project\SoftPLC\classicladder-main\src\edit.c", "C", 0, 0, 1340, 0, 161, 71, 1572
"c:\home\project\SoftPLC\classicladder-main\src\edit.h", "C++", 0, 28, 0, 0, 0, 3, 31
"c:\home\project\SoftPLC\classicladder-main\src\edit_copy.c", "C", 0, 0, 158, 0, 27, 15, 200
"c:\home\project\SoftPLC\classicladder-main\src\edit_copy.h", "C++", 0, 7, 0, 0, 0, 3, 10
"c:\home\project\SoftPLC\classicladder-main\src\edit_gtk.c", "C", 0, 0, 694, 0, 76, 48, 818
"c:\home\project\SoftPLC\classicladder-main\src\edit_gtk.h", "C++", 0, 51, 0, 0, 0, 3, 54
"c:\home\project\SoftPLC\classicladder-main\src\edit_sequential.c", "C", 0, 0, 1018, 0, 72, 52, 1142
"c:\home\project\SoftPLC\classicladder-main\src\edit_sequential.h", "C++", 0, 7, 0, 0, 0, 1, 8
"c:\home\project\SoftPLC\classicladder-main\src\editproperties_gtk.c", "C", 0, 0, 240, 0, 108, 34, 382
"c:\home\project\SoftPLC\classicladder-main\src\editproperties_gtk.h", "C++", 0, 4, 0, 0, 0, 1, 5
"c:\home\project\SoftPLC\classicladder-main\src\files.c", "C", 0, 0, 2030, 0, 158, 93, 2281
"c:\home\project\SoftPLC\classicladder-main\src\files.h", "C++", 0, 49, 0, 0, 3, 10, 62
"c:\home\project\SoftPLC\classicladder-main\src\files_project.c", "C", 0, 0, 401, 0, 111, 41, 553
"c:\home\project\SoftPLC\classicladder-main\src\files_project.h", "C++", 0, 12, 0, 0, 0, 4, 16
"c:\home\project\SoftPLC\classicladder-main\src\files_sequential.c", "C", 0, 0, 217, 0, 26, 14, 257
"c:\home\project\SoftPLC\classicladder-main\src\files_sequential.h", "C++", 0, 2, 0, 0, 0, 2, 4
"c:\home\project\SoftPLC\classicladder-main\src\frames_log_buffers.c", "C", 0, 0, 192, 0, 30, 21, 243
"c:\home\project\SoftPLC\classicladder-main\src\frames_log_buffers.h", "C++", 0, 26, 0, 0, 4, 10, 40
"c:\home\project\SoftPLC\classicladder-main\src\global.h", "C++", 0, 69, 0, 0, 20, 25, 114
"c:\home\project\SoftPLC\classicladder-main\src\hardware.c", "C", 0, 0, 589, 0, 62, 41, 692
"c:\home\project\SoftPLC\classicladder-main\src\hardware.h", "C++", 0, 9, 0, 0, 4, 7, 20
"c:\home\project\SoftPLC\classicladder-main\src\icons_gtk.c", "C", 0, 0, 134, 0, 32, 18, 184
"c:\home\project\SoftPLC\classicladder-main\src\icons_gtk.h", "C++", 0, 3, 0, 0, 0, 2, 5
"c:\home\project\SoftPLC\classicladder-main\src\ladderlib.c", "C", 0, 0, 112, 0, 26, 16, 154
"c:\home\project\SoftPLC\classicladder-main\src\log_events.c", "C", 0, 0, 673, 0, 97, 38, 808
"c:\home\project\SoftPLC\classicladder-main\src\log_events.h", "C++", 0, 22, 0, 0, 0, 7, 29
"c:\home\project\SoftPLC\classicladder-main\src\log_events_gtk.c", "C", 0, 0, 296, 0, 51, 42, 389
"c:\home\project\SoftPLC\classicladder-main\src\log_events_gtk.h", "C++", 0, 6, 0, 0, 0, 2, 8
"c:\home\project\SoftPLC\classicladder-main\src\manager.c", "C", 0, 0, 283, 0, 41, 24, 348
"c:\home\project\SoftPLC\classicladder-main\src\manager.h", "C++", 0, 13, 0, 0, 0, 2, 15
"c:\home\project\SoftPLC\classicladder-main\src\manager_gtk.c", "C", 0, 0, 545, 0, 96, 56, 697
"c:\home\project\SoftPLC\classicladder-main\src\manager_gtk.h", "C++", 0, 13, 0, 0, 1, 1, 15
"c:\home\project\SoftPLC\classicladder-main\src\manager_gtk_OLD_CLIST.c", "C", 0, 0, 382, 0, 75, 41, 498
"c:\home\project\SoftPLC\classicladder-main\src\matplc.conf", "Properties", 0, 0, 0, 7, 8, 8, 23
"c:\home\project\SoftPLC\classicladder-main\src\menu_and_toolbar_gtk.c", "C", 0, 0, 535, 0, 42, 40, 617
"c:\home\project\SoftPLC\classicladder-main\src\menu_and_toolbar_gtk.h", "C++", 0, 20, 0, 0, 0, 2, 22
"c:\home\project\SoftPLC\classicladder-main\src\modem.c", "C", 0, 0, 287, 0, 46, 26, 359
"c:\home\project\SoftPLC\classicladder-main\src\modem.h", "C++", 0, 10, 0, 0, 0, 3, 13
"c:\home\project\SoftPLC\classicladder-main\src\module_rtai.c", "C", 0, 0, 95, 0, 29, 16, 140
"c:\home\project\SoftPLC\classicladder-main\src\module_rtlinux.c", "C", 0, 0, 71, 0, 19, 19, 109
"c:\home\project\SoftPLC\classicladder-main\src\module_rtlinux.h", "C++", 0, 1, 0, 0, 0, 2, 3
"c:\home\project\SoftPLC\classicladder-main\src\monitor_protocol.c", "C", 0, 0, 1171, 0, 232, 118, 1521
"c:\home\project\SoftPLC\classicladder-main\src\monitor_protocol.h", "C++", 0, 36, 0, 0, 0, 11, 47
"c:\home\project\SoftPLC\classicladder-main\src\monitor_protocol_adds_serial.c", "C", 0, 0, 77, 0, 55, 11, 143
"c:\home\project\SoftPLC\classicladder-main\src\monitor_protocol_adds_serial.h", "C++", 0, 17, 0, 0, 0, 10, 27
"c:\home\project\SoftPLC\classicladder-main\src\monitor_serial_config_window_gtk.c", "C", 0, 0, 87, 0, 23, 15, 125
"c:\home\project\SoftPLC\classicladder-main\src\monitor_serial_config_window_gtk.h", "C++", 0, 2, 0, 0, 0, 2, 4
"c:\home\project\SoftPLC\classicladder-main\src\monitor_sockets_udp.c", "C", 0, 0, 115, 0, 33, 17, 165
"c:\home\project\SoftPLC\classicladder-main\src\monitor_sockets_udp.h", "C++", 0, 41, 0, 0, 0, 14, 55
"c:\home\project\SoftPLC\classicladder-main\src\monitor_threads.c", "C", 0, 0, 606, 0, 69, 39, 714
"c:\home\project\SoftPLC\classicladder-main\src\monitor_threads.h", "C++", 0, 7, 0, 0, 1, 3, 11
"c:\home\project\SoftPLC\classicladder-main\src\monitor_transfer.c", "C", 0, 0, 635, 0, 80, 28, 743
"c:\home\project\SoftPLC\classicladder-main\src\monitor_transfer.h", "C++", 0, 27, 0, 0, 0, 10, 37
"c:\home\project\SoftPLC\classicladder-main\src\monitor_windows_gtk.c", "C", 0, 0, 530, 0, 102, 58, 690
"c:\home\project\SoftPLC\classicladder-main\src\monitor_windows_gtk.h", "C++", 0, 23, 0, 0, 2, 7, 32
"c:\home\project\SoftPLC\classicladder-main\src\network_config_window_gtk.c", "C", 0, 0, 95, 0, 23, 16, 134
"c:\home\project\SoftPLC\classicladder-main\src\network_config_window_gtk.h", "C++", 0, 2, 0, 0, 0, 3, 5
"c:\home\project\SoftPLC\classicladder-main\src\preferences.c", "C", 0, 0, 468, 0, 33, 19, 520
"c:\home\project\SoftPLC\classicladder-main\src\preferences.h", "C++", 0, 16, 0, 0, 0, 5, 21
"c:\home\project\SoftPLC\classicladder-main\src\print_gnome.c", "C", 0, 0, 174, 0, 27, 40, 241
"c:\home\project\SoftPLC\classicladder-main\src\print_gnome.h", "C++", 0, 2, 0, 0, 0, 2, 4
"c:\home\project\SoftPLC\classicladder-main\src\print_gtk.c", "C", 0, 0, 273, 0, 28, 39, 340
"c:\home\project\SoftPLC\classicladder-main\src\print_gtk.h", "C++", 0, 2, 0, 0, 0, 2, 4
"c:\home\project\SoftPLC\classicladder-main\src\protocol_modbus_defines.h", "C++", 0, 24, 0, 0, 3, 8, 35
"c:\home\project\SoftPLC\classicladder-main\src\protocol_modbus_master.c", "C", 0, 0, 762, 0, 74, 32, 868
"c:\home\project\SoftPLC\classicladder-main\src\protocol_modbus_master.h", "C++", 0, 70, 0, 0, 9, 12, 91
"c:\home\project\SoftPLC\classicladder-main\src\protocol_modbus_slave.c", "C", 0, 0, 255, 0, 53, 21, 329
"c:\home\project\SoftPLC\classicladder-main\src\protocol_modbus_slave.h", "C++", 0, 4, 0, 0, 0, 3, 7
"c:\home\project\SoftPLC\classicladder-main\src\search.c", "C", 0, 0, 378, 0, 40, 35, 453
"c:\home\project\SoftPLC\classicladder-main\src\search.h", "C++", 0, 9, 0, 0, 0, 3, 12
"c:\home\project\SoftPLC\classicladder-main\src\sequential.h", "C++", 0, 58, 0, 0, 44, 22, 124
"c:\home\project\SoftPLC\classicladder-main\src\serial_common.h", "C++", 0, 17, 0, 0, 2, 4, 23
"c:\home\project\SoftPLC\classicladder-main\src\serial_linux.c", "C", 0, 0, 249, 0, 44, 24, 317
"c:\home\project\SoftPLC\classicladder-main\src\serial_test_rs485.c", "C", 0, 0, 59, 0, 4, 10, 73
"c:\home\project\SoftPLC\classicladder-main\src\serial_win.c", "C", 0, 0, 171, 0, 40, 21, 232
"c:\home\project\SoftPLC\classicladder-main\src\socket_modbus_master.c", "C", 0, 0, 369, 0, 57, 40, 466
"c:\home\project\SoftPLC\classicladder-main\src\socket_modbus_master.h", "C++", 0, 6, 0, 0, 0, 2, 8
"c:\home\project\SoftPLC\classicladder-main\src\socket_server.c", "C", 0, 0, 215, 0, 38, 28, 281
"c:\home\project\SoftPLC\classicladder-main\src\socket_server.h", "C++", 0, 3, 0, 0, 0, 4, 7
"c:\home\project\SoftPLC\classicladder-main\src\spy_vars_gtk.c", "C", 0, 0, 883, 0, 173, 95, 1151
"c:\home\project\SoftPLC\classicladder-main\src\spy_vars_gtk.h", "C++", 0, 14, 0, 0, 1, 2, 17
"c:\home\project\SoftPLC\classicladder-main\src\symbols.c", "C", 0, 0, 142, 0, 48, 14, 204
"c:\home\project\SoftPLC\classicladder-main\src\symbols.h", "C++", 0, 4, 0, 0, 0, 2, 6
"c:\home\project\SoftPLC\classicladder-main\src\symbols_gtk.c", "C", 0, 0, 194, 0, 47, 30, 271
"c:\home\project\SoftPLC\classicladder-main\src\symbols_gtk.h", "C++", 0, 5, 0, 0, 0, 2, 7
"c:\home\project\SoftPLC\classicladder-main\src\target_embedded_plc_486.h", "C++", 0, 11, 0, 0, 10, 7, 28
"c:\home\project\SoftPLC\classicladder-main\src\target_embedded_plc_arietta.h", "C++", 0, 8, 0, 0, 7, 7, 22
"c:\home\project\SoftPLC\classicladder-main\src\target_embedded_raspberrypi.h", "C++", 0, 4, 0, 0, 9, 7, 20
"c:\home\project\SoftPLC\classicladder-main\src\tasks.c", "C", 0, 0, 499, 0, 45, 39, 583
"c:\home\project\SoftPLC\classicladder-main\src\tasks.h", "C++", 0, 25, 0, 0, 1, 12, 38
"c:\home\project\SoftPLC\classicladder-main\src\tasks_OLD_beforeXeno3.c", "C", 0, 0, 403, 0, 36, 31, 470
"c:\home\project\SoftPLC\classicladder-main\src\time_and_rtc.c", "C", 0, 0, 208, 0, 39, 17, 264
"c:\home\project\SoftPLC\classicladder-main\src\time_and_rtc.h", "C++", 0, 14, 0, 0, 1, 6, 21
"c:\home\project\SoftPLC\classicladder-main\src\time_test_rtc.c", "C", 0, 0, 44, 0, 4, 8, 56
"c:\home\project\SoftPLC\classicladder-main\src\vars_access.c", "C", 0, 0, 381, 0, 37, 17, 435
"c:\home\project\SoftPLC\classicladder-main\src\vars_access.h", "C++", 0, 12, 0, 0, 1, 3, 16
"c:\home\project\SoftPLC\classicladder-main\src\vars_browser_gtk.c", "C", 0, 0, 246, 0, 45, 33, 324
"c:\home\project\SoftPLC\classicladder-main\src\vars_browser_gtk.h", "C++", 0, 4, 0, 0, 0, 3, 7
"c:\home\project\SoftPLC\classicladder-main\src\vars_mat.c", "C", 0, 0, 70, 0, 29, 14, 113
"c:\home\project\SoftPLC\classicladder-main\src\vars_names.c", "C", 0, 0, 680, 0, 65, 41, 786
"c:\home\project\SoftPLC\classicladder-main\src\vars_names.h", "C++", 0, 4, 0, 0, 0, 4, 8
"c:\home\project\SoftPLC\classicladder-main\src\vars_names_list.c", "C", 0, 0, 77, 0, 25, 28, 130
"c:\home\project\SoftPLC\classicladder-main\src\vars_system.c", "C", 0, 0, 138, 0, 34, 18, 190
"c:\home\project\SoftPLC\classicladder-main\src\vars_system.h", "C++", 0, 3, 0, 0, 0, 4, 7
"c:\home\project\SoftPLC\classicladder-main\wiringSam\Makefile", "Makefile", 14, 0, 0, 0, 3, 9, 26
"c:\home\project\SoftPLC\classicladder-main\wiringSam\test_at91sam_gpio.c", "C", 0, 0, 28, 0, 17, 8, 53
"c:\home\project\SoftPLC\classicladder-main\wiringSam\wiringSam.c", "C", 0, 0, 163, 0, 64, 35, 262
"c:\home\project\SoftPLC\classicladder-main\wiringSam\wiringSam.h", "C++", 0, 80, 0, 0, 25, 29, 134
"Total", "-", 395, 6142, 29475, 7, 4905, 3077, 44001